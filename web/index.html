<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutter_application_1">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">


  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>flutter_application_1</title>
  <link rel="manifest" href="manifest.json">

  <style>
  @font-face {
    font-family: 'SatoshiR';
    src: url('assets/fonts/Satoshi-Regular.otf') format('opentype');
    font-weight: 400;
  }
  @font-face {
    font-family: 'SatoshiM';
    src: url('assets/fonts/Satoshi-Medium.otf') format('opentype');
    font-weight: 500;
  }
  @font-face {
    font-family: 'SatoshiB';
    src: url('assets/fonts/Satoshi-Bold.otf') format('opentype');
    font-weight: 700;
  }
  @font-face {
    font-family: 'DegularR';
    src: url('assets/fonts/DegularDisplay-Regular.otf') format('opentype');
    font-weight: 400;
  }
  @font-face {
    font-family: 'DegularM';
    src: url('assets/fonts/DegularDisplay-Medium.otf') format('opentype');
    font-weight: 500;
  }
</style>
</head>


<body>
  <script src="flutter_bootstrap.js" async></script>
  <span class="material-icons">home</span>

</body>
</html>
