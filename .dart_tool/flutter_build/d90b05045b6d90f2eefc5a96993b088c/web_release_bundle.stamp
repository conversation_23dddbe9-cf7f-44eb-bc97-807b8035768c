{"inputs": ["/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/pubspec.yaml", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/.dart_tool/flutter_build/d90b05045b6d90f2eefc5a96993b088c/main.dart.js", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/pubspec.yaml", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/.DS_Store", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/loading.gif", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/Background.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboaring2.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/talk_to_nova.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/Group 123.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/Group 126.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/Group 124.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/Group 125.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/logo.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/hanger.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/star.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/icons/bottom_hanger.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboarding/onboarding1.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboarding/onboarding3.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboarding/onboarding2.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/cardImages/women_handpick.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/cardImages/women_recomm.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/cardImages/men_handpick.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/cardImages/men_recomm.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/style_tags_background.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/Hand green.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/inverted_triangle.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/masculine_inverted_triangle.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/masculine_oval.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/masculine_triangle.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/Hand blue-purple.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/rectangle.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/hourglass.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/hand.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/masculine_trapezoid.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/apple.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/pear.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/onboardingForm/masculine_rectangle.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/splashScreen/logo.json", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/applogo/logo_android.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/applogo/logo_ios.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/no_network.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/archive.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/almirahno.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/almirah.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/tshirt_no.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/history.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/emptyImages/tshirt.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/loader/loading.json", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/loader/loading.gif", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/outfits.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/cloth.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/pick.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/Frame 102.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/color.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/Frame 103.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/profile.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/explore.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/staticImages/styleguide.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/MaterialIcons-Regular.ttf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/Satoshi-Regular.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/Satoshi-Medium.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/Satoshi-Bold.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/DegularDisplay-Regular.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/assets/fonts/DegularDisplay-Medium.otf", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito_dart-0.11.8/lib/src/workers/workers.min.js", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito_dart-0.11.8/lib/src/workers/workers.min.js.map", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_authenticator-2.3.2/assets/social-buttons/google.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_authenticator-2.3.2/assets/social-buttons/SocialIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage_dart-0.5.3/lib/src/worker/workers.min.js", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage_dart-0.5.3/lib/src/worker/workers.min.js.map", "/Users/<USER>/.pub-cache/hosted/pub.dev/any_link_preview-3.0.3/lib/assets/giphy.gif", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_feather_icons-2.0.0+1/fonts/feather.ttf", "/Users/<USER>/Downloads/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Downloads/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_analytics_pinpoint-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_analytics_pinpoint_dart-0.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_api-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_api_dart-0.5.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito_dart-0.11.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_authenticator-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_core-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_db_common-0.4.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_db_common_dart-0.4.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_flutter-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage-0.5.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage_dart-0.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_storage_s3-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/amplify_storage_s3_dart-0.4.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/animated_text_kit-4.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/any_link_preview-3.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/aws_common-0.7.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/aws_signature_v4-0.6.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/azblob-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.11.0+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.10+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crclib-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crop_your_image-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/drift-2.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_feather_icons-2.0.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-12.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http2-2.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/location-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/oauth2-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/os_detect-2.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-5.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pull_to_refresh-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rename_app-1.6.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus_platform_interface-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/smithy-0.7.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/smithy_aws-0.7.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_validator-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_html-2.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/worker_bee-0.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/Users/<USER>/Downloads/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/Downloads/flutter/packages/flutter/LICENSE", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/DOES_NOT_EXIST_RERUN_FOR_WILDCARD268643444", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/index.html", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/.DS_Store", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/favicon.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/icons/Icon-192.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/icons/Icon-maskable-192.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/icons/Icon-maskable-512.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/icons/Icon-512.png", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/manifest.json", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/.DS_Store", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/Satoshi-Bold.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/Satoshi-Regular.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/MaterialIcons-Regular.ttf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/DegularDisplay-Medium.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/DegularDisplay-Regular.otf", "/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/web/assets/fonts/Satoshi-Medium.otf"], "outputs": ["/Users/<USER>/MonovaAIProjects/digital_wardrobe_web_app/build/web/main.dart.js", "build/web/assets/assets/.DS_Store", "build/web/assets/assets/loading.gif", "build/web/assets/assets/Background.png", "build/web/assets/assets/onboaring2.png", "build/web/assets/assets/icons/talk_to_nova.png", "build/web/assets/assets/icons/Group%20123.png", "build/web/assets/assets/icons/Group%20126.png", "build/web/assets/assets/icons/Group%20124.png", "build/web/assets/assets/icons/Group%20125.png", "build/web/assets/assets/icons/logo.png", "build/web/assets/assets/icons/hanger.png", "build/web/assets/assets/icons/star.png", "build/web/assets/assets/icons/bottom_hanger.png", "build/web/assets/assets/onboarding/onboarding1.png", "build/web/assets/assets/onboarding/onboarding3.png", "build/web/assets/assets/onboarding/onboarding2.png", "build/web/assets/assets/cardImages/women_handpick.png", "build/web/assets/assets/cardImages/women_recomm.png", "build/web/assets/assets/cardImages/men_handpick.png", "build/web/assets/assets/cardImages/men_recomm.png", "build/web/assets/assets/onboardingForm/style_tags_background.png", "build/web/assets/assets/onboardingForm/Hand%20green.png", "build/web/assets/assets/onboardingForm/inverted_triangle.png", "build/web/assets/assets/onboardingForm/masculine_inverted_triangle.png", "build/web/assets/assets/onboardingForm/masculine_oval.png", "build/web/assets/assets/onboardingForm/masculine_triangle.png", "build/web/assets/assets/onboardingForm/Hand%20blue-purple.png", "build/web/assets/assets/onboardingForm/rectangle.png", "build/web/assets/assets/onboardingForm/hourglass.png", "build/web/assets/assets/onboardingForm/hand.png", "build/web/assets/assets/onboardingForm/masculine_trapezoid.png", "build/web/assets/assets/onboardingForm/apple.png", "build/web/assets/assets/onboardingForm/pear.png", "build/web/assets/assets/onboardingForm/masculine_rectangle.png", "build/web/assets/assets/splashScreen/logo.json", "build/web/assets/assets/applogo/logo_android.png", "build/web/assets/assets/applogo/logo_ios.png", "build/web/assets/assets/emptyImages/no_network.png", "build/web/assets/assets/emptyImages/archive.png", "build/web/assets/assets/emptyImages/almirahno.png", "build/web/assets/assets/emptyImages/almirah.png", "build/web/assets/assets/emptyImages/tshirt_no.png", "build/web/assets/assets/emptyImages/history.png", "build/web/assets/assets/emptyImages/tshirt.png", "build/web/assets/assets/loader/loading.json", "build/web/assets/assets/loader/loading.gif", "build/web/assets/assets/staticImages/outfits.png", "build/web/assets/assets/staticImages/cloth.png", "build/web/assets/assets/staticImages/pick.png", "build/web/assets/assets/staticImages/Frame%20102.png", "build/web/assets/assets/staticImages/color.png", "build/web/assets/assets/staticImages/Frame%20103.png", "build/web/assets/assets/staticImages/profile.png", "build/web/assets/assets/staticImages/explore.png", "build/web/assets/assets/staticImages/styleguide.png", "build/web/assets/assets/fonts/MaterialIcons-Regular.ttf", "build/web/assets/assets/fonts/Satoshi-Regular.otf", "build/web/assets/assets/fonts/Satoshi-Medium.otf", "build/web/assets/assets/fonts/Satoshi-Bold.otf", "build/web/assets/assets/fonts/DegularDisplay-Regular.otf", "build/web/assets/assets/fonts/DegularDisplay-Medium.otf", "build/web/assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js", "build/web/assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js.map", "build/web/assets/packages/amplify_authenticator/assets/social-buttons/google.png", "build/web/assets/packages/amplify_authenticator/assets/social-buttons/SocialIcons.ttf", "build/web/assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js", "build/web/assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js.map", "build/web/assets/packages/any_link_preview/lib/assets/giphy.gif", "build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "build/web/assets/packages/flutter_feather_icons/fonts/feather.ttf", "build/web/assets/fonts/MaterialIcons-Regular.otf", "build/web/assets/shaders/ink_sparkle.frag", "build/web/assets/AssetManifest.json", "build/web/assets/AssetManifest.bin", "build/web/assets/AssetManifest.bin.json", "build/web/assets/FontManifest.json", "build/web/assets/NOTICES", "build/web/.DS_Store", "build/web/favicon.png", "build/web/icons/Icon-192.png", "build/web/icons/Icon-maskable-192.png", "build/web/icons/Icon-maskable-512.png", "build/web/icons/Icon-512.png", "build/web/manifest.json", "build/web/assets/.DS_Store", "build/web/assets/fonts/Satoshi-Bold.otf", "build/web/assets/fonts/Satoshi-Regular.otf", "build/web/assets/fonts/MaterialIcons-Regular.ttf", "build/web/assets/fonts/DegularDisplay-Medium.otf", "build/web/assets/fonts/DegularDisplay-Regular.otf", "build/web/assets/fonts/Satoshi-Medium.otf"]}