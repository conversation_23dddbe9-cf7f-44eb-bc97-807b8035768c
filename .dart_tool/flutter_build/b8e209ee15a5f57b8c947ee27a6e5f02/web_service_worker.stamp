{"inputs": ["build/web/flutter_bootstrap.js", "build/web/version.json", "build/web/index.html", "build/web/main.dart.js", "build/web/flutter.js", "build/web/favicon.png", "build/web/icons/Icon-192.png", "build/web/icons/Icon-maskable-192.png", "build/web/icons/Icon-maskable-512.png", "build/web/icons/Icon-512.png", "build/web/manifest.json", "build/web/assets/AssetManifest.json", "build/web/assets/NOTICES", "build/web/assets/FontManifest.json", "build/web/assets/AssetManifest.bin.json", "build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "build/web/assets/packages/flutter_feather_icons/fonts/feather.ttf", "build/web/assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js.map", "build/web/assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js", "build/web/assets/packages/any_link_preview/lib/assets/giphy.gif", "build/web/assets/packages/amplify_authenticator/assets/social-buttons/SocialIcons.ttf", "build/web/assets/packages/amplify_authenticator/assets/social-buttons/google.png", "build/web/assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js.map", "build/web/assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js", "build/web/assets/shaders/ink_sparkle.frag", "build/web/assets/AssetManifest.bin", "build/web/assets/fonts/Satoshi-Bold.otf", "build/web/assets/fonts/Satoshi-Regular.otf", "build/web/assets/fonts/MaterialIcons-Regular.ttf", "build/web/assets/fonts/DegularDisplay-Medium.otf", "build/web/assets/fonts/MaterialIcons-Regular.otf", "build/web/assets/fonts/DegularDisplay-Regular.otf", "build/web/assets/fonts/Satoshi-Medium.otf", "build/web/assets/assets/staticImages/outfits.png", "build/web/assets/assets/staticImages/cloth.png", "build/web/assets/assets/staticImages/pick.png", "build/web/assets/assets/staticImages/color.png", "build/web/assets/assets/staticImages/profile.png", "build/web/assets/assets/staticImages/Frame%20103.png", "build/web/assets/assets/staticImages/Frame%20102.png", "build/web/assets/assets/staticImages/explore.png", "build/web/assets/assets/staticImages/styleguide.png", "build/web/assets/assets/cardImages/women_handpick.png", "build/web/assets/assets/cardImages/women_recomm.png", "build/web/assets/assets/cardImages/men_handpick.png", "build/web/assets/assets/cardImages/men_recomm.png", "build/web/assets/assets/emptyImages/no_network.png", "build/web/assets/assets/emptyImages/archive.png", "build/web/assets/assets/emptyImages/almirahno.png", "build/web/assets/assets/emptyImages/almirah.png", "build/web/assets/assets/emptyImages/tshirt_no.png", "build/web/assets/assets/emptyImages/history.png", "build/web/assets/assets/emptyImages/tshirt.png", "build/web/assets/assets/loading.gif", "build/web/assets/assets/Background.png", "build/web/assets/assets/splashScreen/logo.json", "build/web/assets/assets/loader/loading.json", "build/web/assets/assets/loader/loading.gif", "build/web/assets/assets/icons/talk_to_nova.png", "build/web/assets/assets/icons/logo.png", "build/web/assets/assets/icons/hanger.png", "build/web/assets/assets/icons/star.png", "build/web/assets/assets/icons/bottom_hanger.png", "build/web/assets/assets/icons/Group%20123.png", "build/web/assets/assets/icons/Group%20126.png", "build/web/assets/assets/icons/Group%20124.png", "build/web/assets/assets/icons/Group%20125.png", "build/web/assets/assets/onboaring2.png", "build/web/assets/assets/fonts/Satoshi-Bold.otf", "build/web/assets/assets/fonts/Satoshi-Regular.otf", "build/web/assets/assets/fonts/MaterialIcons-Regular.ttf", "build/web/assets/assets/fonts/DegularDisplay-Medium.otf", "build/web/assets/assets/fonts/DegularDisplay-Regular.otf", "build/web/assets/assets/fonts/Satoshi-Medium.otf", "build/web/assets/assets/onboardingForm/style_tags_background.png", "build/web/assets/assets/onboardingForm/inverted_triangle.png", "build/web/assets/assets/onboardingForm/masculine_inverted_triangle.png", "build/web/assets/assets/onboardingForm/masculine_oval.png", "build/web/assets/assets/onboardingForm/masculine_triangle.png", "build/web/assets/assets/onboardingForm/rectangle.png", "build/web/assets/assets/onboardingForm/hourglass.png", "build/web/assets/assets/onboardingForm/Hand%20green.png", "build/web/assets/assets/onboardingForm/hand.png", "build/web/assets/assets/onboardingForm/masculine_trapezoid.png", "build/web/assets/assets/onboardingForm/apple.png", "build/web/assets/assets/onboardingForm/Hand%20blue-purple.png", "build/web/assets/assets/onboardingForm/pear.png", "build/web/assets/assets/onboardingForm/masculine_rectangle.png", "build/web/assets/assets/applogo/logo_android.png", "build/web/assets/assets/applogo/logo_ios.png", "build/web/assets/assets/onboarding/onboarding1.png", "build/web/assets/assets/onboarding/onboarding3.png", "build/web/assets/assets/onboarding/onboarding2.png", "build/web/canvaskit/skwasm.js", "build/web/canvaskit/skwasm.js.symbols", "build/web/canvaskit/canvaskit.js.symbols", "build/web/canvaskit/skwasm.wasm", "build/web/canvaskit/chromium/canvaskit.js.symbols", "build/web/canvaskit/chromium/canvaskit.js", "build/web/canvaskit/chromium/canvaskit.wasm", "build/web/canvaskit/canvaskit.js", "build/web/canvaskit/canvaskit.wasm", "build/web/canvaskit/skwasm.worker.js"], "outputs": ["build/web/flutter_service_worker.js"]}