{"configVersion": 2, "packages": [{"name": "amplify_analytics_pinpoint", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_analytics_pinpoint-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_analytics_pinpoint_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_analytics_pinpoint_dart-0.4.7", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_api-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_api_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_api_dart-0.5.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_auth_cognito", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_auth_cognito_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_auth_cognito_dart-0.11.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_authenticator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_authenticator-2.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_core-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_db_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_db_common-0.4.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_db_common_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_db_common_dart-0.4.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_flutter-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_secure_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage-0.5.7", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_secure_storage_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_secure_storage_dart-0.5.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_storage_s3", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_storage_s3-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "amplify_storage_s3_dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/amplify_storage_s3_dart-0.4.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "animated_text_kit", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/animated_text_kit-4.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "any_link_preview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/any_link_preview-3.0.3", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "aws_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/aws_common-0.7.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "aws_signature_v4", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/aws_signature_v4-0.6.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "azblob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/azblob-4.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "camera", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.11.0+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "camera_android_camerax", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.10+3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "camera_avfoundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "camera_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "camera_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "carousel_slider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "change_app_package_name", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "charcode", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "connectivity_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crclib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crclib-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "crop_your_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crop_your_image-2.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "drift", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/drift-2.18.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_advanced_segment", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_cache_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_feather_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_feather_icons-2.0.0+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_launcher_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_local_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_localizations", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "geolocator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-12.0.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "geolocator_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "google_fonts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http2-2.3.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "jwt_decoder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "location", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/location-6.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "location_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "location_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "lottie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.2.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nm", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "oauth2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/oauth2-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "octo_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "os_detect", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/os_detect-2.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_parsing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pinput", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pinput-5.0.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pull_to_refresh", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pull_to_refresh-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "qr", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "qr_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "rename_app", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rename_app-1.6.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "retry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sensors_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-6.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sensors_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "share_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "share_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shimmer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "smithy", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/smithy-0.7.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "smithy_aws", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/smithy_aws-0.7.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqlite3", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "string_validator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_validator-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "tuple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "universal_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_html-2.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_codec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "worker_bee", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/worker_bee-0.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "monova_ai_stylist", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.5"}], "generated": "2025-06-02T06:49:57.559382Z", "generator": "pub", "generatorVersion": "3.5.4", "flutterRoot": "file:///Users/<USER>/development/flutter", "flutterVersion": "3.24.5", "pubCache": "file:///Users/<USER>/.pub-cache"}