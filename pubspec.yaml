name: monova_ai_stylist
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+15

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  amplify_api: ^2.0.0
  amplify_flutter: ^2.0.0
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  amplify_auth_cognito: ^2.5.0
  amplify_authenticator: ^2.3.1
  animated_text_kit: ^4.2.2
  share_plus: ^10.0.0
  google_fonts: ^6.2.1
  image_picker: ^1.1.2
  flutter_launcher_icons: ^0.13.1 
  shared_preferences: ^2.3.5
  rename_app: ^1.6.2
  shimmer: ^3.0.0
  pinput: ^5.0.1
  http: ^1.3.0
  url_launcher: ^6.3.1
  flutter_local_notifications: ^18.0.1
  package_info_plus: ^8.1.3
  web_socket_channel: ^2.4.5
  cached_network_image: ^3.4.0
  amplify_storage_s3: ^2.6.0
  azblob: ^4.0.0
  flutter_advanced_segment: ^3.0.2
  flutter_feather_icons: ^2.0.0+1
  any_link_preview: ^3.0.3
  crop_your_image: ^2.0.0
  geolocator: ^12.0.0
  sensors_plus: ^6.1.1
  connectivity_plus: ^6.1.3
  lottie: ^3.2.0
  camera: ^0.11.0+2
  carousel_slider: ^5.0.0
  location: ^6.0.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  flutter_cache_manager: ^3.4.1
  pull_to_refresh: ^2.0.0
  jwt_decoder: ^2.0.1
  html: ^0.15.6
  universal_html: ^2.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  change_app_package_name: ^1.4.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/onboarding/
    - assets/cardImages/
    - assets/onboardingForm/
    - assets/splashScreen/
    - assets/applogo/
    - assets/images/
    - assets/emptyImages/
    - assets/loader/
    - assets/staticImages/
    - assets/fonts/MaterialIcons-Regular.ttf
    - assets/fonts/Satoshi-Regular.otf
    - assets/fonts/Satoshi-Medium.otf
    - assets/fonts/Satoshi-Bold.otf
    - assets/fonts/DegularDisplay-Regular.otf
    - assets/fonts/DegularDisplay-Medium.otf
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SatoshiR
      fonts:
        - asset: assets/fonts/Satoshi-Regular.otf

    - family: SatoshiM
      fonts:
        - asset: assets/fonts/Satoshi-Medium.otf
    
    - family: SatoshiB
      fonts:
        - asset: assets/fonts/Satoshi-Bold.otf

    - family: DegularR
      fonts:
        - asset: assets/fonts/DegularDisplay-Regular.otf   
        
    - family: DegularM
      fonts:
        - asset: assets/fonts/DegularDisplay-Medium.otf

    - family: MaterialIcons
      fonts:
      - asset: assets/fonts/MaterialIcons-Regular.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path_android: "assets/applogo/logo_android.png"
  image_path_ios: "assets/applogo/logo_ios.png"
  min_sdk_android: 21
