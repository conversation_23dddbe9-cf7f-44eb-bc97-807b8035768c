'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"flutter_bootstrap.js": "c18b58cb06dbbadf0f935788d12f2865",
"version.json": "75065edb02eb66f3c8402a18c8bfc5d3",
"index.html": "14dfe9c73ee87a87c7511d8b9519db25",
"/": "14dfe9c73ee87a87c7511d8b9519db25",
"main.dart.js": "77965410b6b3b5816c6e69416c8cc332",
"flutter.js": "f393d3c16b631f36852323de8e583132",
"favicon.png": "5dcef449791fa27946b3d35ad8803796",
"icons/Icon-192.png": "ac9a721a12bbc803b44f645561ecb1e1",
"icons/Icon-maskable-192.png": "c457ef57daa1d16f64b27b786ec2ea3c",
"icons/Icon-maskable-512.png": "301a7604d45b3e739efc881eb04896ea",
"icons/Icon-512.png": "96e752610906ba2a93c65f8abe1645f1",
"manifest.json": "fd091ea5bc8f3ccc10f74bd67179fb6a",
"assets/AssetManifest.json": "4b96d6a7d49867b6afb657e22d3ae978",
"assets/NOTICES": "dc2f26d18091143ded9493eda1198729",
"assets/FontManifest.json": "d31115b9d4b5ab1deb26606fe999d332",
"assets/AssetManifest.bin.json": "248178f2ac0515411b1844ac181cabad",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "e986ebe42ef785b27164c36a9abc7818",
"assets/packages/flutter_feather_icons/fonts/feather.ttf": "40469726c5ed792185741388e68dd9e8",
"assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js.map": "54f06a8ed79c628c13d5825e2a2c4bea",
"assets/packages/amplify_auth_cognito_dart/lib/src/workers/workers.min.js": "cce02155f6563d93df68d54398acb48d",
"assets/packages/any_link_preview/lib/assets/giphy.gif": "b0db8189c4cfba8340d61b1e72b1acdc",
"assets/packages/amplify_authenticator/assets/social-buttons/SocialIcons.ttf": "1566e823935d5fe33901f5a074480a20",
"assets/packages/amplify_authenticator/assets/social-buttons/google.png": "a1e1d65465c69a65f8d01226ff5237ec",
"assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js.map": "3ce9ff7bf3f1ff4fd8c105b33a06e4a1",
"assets/packages/amplify_secure_storage_dart/lib/src/worker/workers.min.js": "3dce3007b60184273c34857117a97551",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"assets/AssetManifest.bin": "e1a03af2f39d99cf6e8c74fb00ed3e61",
"assets/fonts/Satoshi-Bold.otf": "4a6fdcfc68ad464e8a9811e4edcacf00",
"assets/fonts/Satoshi-Regular.otf": "177a4dda04b52dedbd966942e932c5dc",
"assets/fonts/MaterialIcons-Regular.ttf": "a37b0c01c0baf1888ca812cc0508f6e2",
"assets/fonts/DegularDisplay-Medium.otf": "35023b5a1e3be4d66439e8e6fcce0c03",
"assets/fonts/MaterialIcons-Regular.otf": "e7069dfd19b331be16bed984668fe080",
"assets/fonts/DegularDisplay-Regular.otf": "8d9a635091b9ed50b6ec4c65402fd437",
"assets/fonts/Satoshi-Medium.otf": "378def5c1f4df7eb6554a88608893391",
"assets/assets/staticImages/outfits.png": "831799dc0f09e82800cd64b86a64be8f",
"assets/assets/staticImages/cloth.png": "d85e056360c6a187c2b4b4c923f4a5b1",
"assets/assets/staticImages/pick.png": "bdc454cca6fa224637f44fe1cda911dd",
"assets/assets/staticImages/color.png": "6667308a1556dc1851f87742a59caaf2",
"assets/assets/staticImages/profile.png": "391d611f4035c8f839556411b5f184a0",
"assets/assets/staticImages/Frame%2520103.png": "fb338feffcae83aa4c5675ccd9a9511f",
"assets/assets/staticImages/Frame%2520102.png": "cbc87e5216c957a3a483a8180fd83118",
"assets/assets/staticImages/explore.png": "63ed55003e97edcef399f5134bd74d2d",
"assets/assets/staticImages/styleguide.png": "65088f3771427766dceda19040652393",
"assets/assets/cardImages/women_handpick.png": "c262066b6cd23fee3b49322988a44aab",
"assets/assets/cardImages/women_recomm.png": "cebbbeeec51268a8be4ca05c1231d110",
"assets/assets/cardImages/men_handpick.png": "def0ed78283b4ed9bac81517b4c93efd",
"assets/assets/cardImages/men_recomm.png": "ca8a392f0993b6b626a480090312994f",
"assets/assets/emptyImages/no_network.png": "7a12b6ac3d403b28635b898ab7fcebac",
"assets/assets/emptyImages/archive.png": "6c6623df81153b64eebeac55c73b5e13",
"assets/assets/emptyImages/almirahno.png": "531e61b0f978359d93e4aef6d56c9cb2",
"assets/assets/emptyImages/almirah.png": "191a02aa94efe8399d5fe4b4d132276a",
"assets/assets/emptyImages/tshirt_no.png": "ff8cdc38aa5153e9f7724f64f36a072d",
"assets/assets/emptyImages/history.png": "6fea5ae8f6fbdc4d234e8c3a1c13d348",
"assets/assets/emptyImages/tshirt.png": "b2f662fb9f2d53a4d04997966e8c6032",
"assets/assets/loading.gif": "f246f51468a64a2f61e973782bd50904",
"assets/assets/Background.png": "b76c6cd1eb9b23974ffbba85b2604dd5",
"assets/assets/splashScreen/logo.json": "7588e8f5f2db013c6126b25ff8b078cf",
"assets/assets/loader/loading.json": "56adbfe6807d1e22a70a01c08d4b5f8e",
"assets/assets/loader/loading.gif": "809e7272d3bb6373e0f2dd7b66f2fc5c",
"assets/assets/icons/talk_to_nova.png": "c3765d293ba42275b7b298bd30dd794e",
"assets/assets/icons/logo.png": "72fe211c53f489f69d035c92a5a42bdb",
"assets/assets/icons/hanger.png": "6351f0e664449825bddc57e7c6faad12",
"assets/assets/icons/star.png": "dde728492aec786b816901943df116fe",
"assets/assets/icons/bottom_hanger.png": "4edc019be874aeb3d94b2dfc6d574549",
"assets/assets/icons/Group%2520123.png": "e4b8101fa6b8a2a142b79492f7c392c0",
"assets/assets/icons/Group%2520126.png": "610210df66453cfd1b11aebd54088645",
"assets/assets/icons/Group%2520124.png": "7000990cbfdcad68af60f8127d238503",
"assets/assets/icons/Group%2520125.png": "357acd15016797f527376bb4f41412f2",
"assets/assets/onboaring2.png": "46371f5c10fcf2864ece98443030dc0b",
"assets/assets/fonts/Satoshi-Bold.otf": "4a6fdcfc68ad464e8a9811e4edcacf00",
"assets/assets/fonts/Satoshi-Regular.otf": "177a4dda04b52dedbd966942e932c5dc",
"assets/assets/fonts/MaterialIcons-Regular.ttf": "a37b0c01c0baf1888ca812cc0508f6e2",
"assets/assets/fonts/DegularDisplay-Medium.otf": "35023b5a1e3be4d66439e8e6fcce0c03",
"assets/assets/fonts/DegularDisplay-Regular.otf": "8d9a635091b9ed50b6ec4c65402fd437",
"assets/assets/fonts/Satoshi-Medium.otf": "378def5c1f4df7eb6554a88608893391",
"assets/assets/onboardingForm/style_tags_background.png": "40e192e7207da83078fabb246fb4319a",
"assets/assets/onboardingForm/inverted_triangle.png": "6bd8b6ba1a3ba80332dfbed32a8df7a2",
"assets/assets/onboardingForm/masculine_inverted_triangle.png": "6bd8b6ba1a3ba80332dfbed32a8df7a2",
"assets/assets/onboardingForm/masculine_oval.png": "1c28e81d791619ea568c718e5800e6e7",
"assets/assets/onboardingForm/masculine_triangle.png": "537ab738ff02beb14df2948bcf304567",
"assets/assets/onboardingForm/rectangle.png": "74647e11bb1b46e9ac0c26555b30a340",
"assets/assets/onboardingForm/hourglass.png": "5f490c6c097a84f49e67ac7ecc6f434f",
"assets/assets/onboardingForm/Hand%2520green.png": "fd1788324bba6f5f4d440c08c8411ae0",
"assets/assets/onboardingForm/hand.png": "2efd4e061f14046151853024e3be7969",
"assets/assets/onboardingForm/masculine_trapezoid.png": "770abf05b82c9a8bc8a7cc8b48969e26",
"assets/assets/onboardingForm/apple.png": "f21924061f63232026d8c5195d984630",
"assets/assets/onboardingForm/Hand%2520blue-purple.png": "f7c05d5d67fa5dc628e44fb1edf040ee",
"assets/assets/onboardingForm/pear.png": "537ab738ff02beb14df2948bcf304567",
"assets/assets/onboardingForm/masculine_rectangle.png": "74647e11bb1b46e9ac0c26555b30a340",
"assets/assets/applogo/logo_android.png": "19290f3b7fcf35326b826b0008d5c9af",
"assets/assets/applogo/logo_ios.png": "ffcc5bf7a49d930f1b3497f1be7c023a",
"assets/assets/onboarding/onboarding1.png": "8ee80b1161954a096be6b3e53b1285d5",
"assets/assets/onboarding/onboarding3.png": "3c91b814fd5d8344186fb19a84c74a06",
"assets/assets/onboarding/onboarding2.png": "3bfe74d5d510166084967f087d17b01b",
"canvaskit/skwasm.js": "694fda5704053957c2594de355805228",
"canvaskit/skwasm.js.symbols": "262f4827a1317abb59d71d6c587a93e2",
"canvaskit/canvaskit.js.symbols": "48c83a2ce573d9692e8d970e288d75f7",
"canvaskit/skwasm.wasm": "9f0c0c02b82a910d12ce0543ec130e60",
"canvaskit/chromium/canvaskit.js.symbols": "a012ed99ccba193cf96bb2643003f6fc",
"canvaskit/chromium/canvaskit.js": "671c6b4f8fcc199dcc551c7bb125f239",
"canvaskit/chromium/canvaskit.wasm": "b1ac05b29c127d86df4bcfbf50dd902a",
"canvaskit/canvaskit.js": "66177750aff65a66cb07bb44b8c6422b",
"canvaskit/canvaskit.wasm": "1f237a213d7370cf95f443d896176460",
"canvaskit/skwasm.worker.js": "89990e8c92bcb123999aa81f7e203b1c"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
