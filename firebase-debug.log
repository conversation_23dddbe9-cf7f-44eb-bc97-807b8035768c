[debug] [2025-06-01T09:28:44.273Z] ----------------------------------------------------------------------
[debug] [2025-06-01T09:28:44.278Z] Command:       /opt/homebrew/Cellar/node/23.10.0_1/bin/node /opt/homebrew/bin/firebase init
[debug] [2025-06-01T09:28:44.278Z] CLI Version:   14.5.1
[debug] [2025-06-01T09:28:44.278Z] Platform:      darwin
[debug] [2025-06-01T09:28:44.278Z] Node Version:  v23.10.0
[debug] [2025-06-01T09:28:44.279Z] Time:          Sun Jun 01 2025 14:58:44 GMT+0530 (India Standard Time)
[debug] [2025-06-01T09:28:44.279Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-01T09:28:44.280Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-06-01T09:28:44.300Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-01T09:28:44.301Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Downloads/Flutter_projects/whatsappIntegration

[debug] [2025-06-01T09:28:45.440Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-06-01T09:28:45.440Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-06-01T09:29:19.329Z] Checked if tokens are valid: false, expires at: 1746445533411
[debug] [2025-06-01T09:29:19.330Z] Checked if tokens are valid: false, expires at: 1746445533411
[debug] [2025-06-01T09:29:19.330Z] > refreshing access token with scopes: []
[debug] [2025-06-01T09:29:19.332Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-01T09:29:19.332Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-01T09:29:19.856Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-01T09:29:19.857Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-01T09:29:19.873Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=100
[debug] [2025-06-01T09:29:20.770Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-01T09:29:20.770Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] i  Don't want to scroll through all your projects? If you know your project ID, you can initialize it directly using firebase init --project <project_id>.
 
[info] i  Using project whatsappintegration-ae33b (whatsappIntegration) 
[info] 
=== Hosting Setup
[debug] [2025-06-01T09:29:34.851Z] Checked if tokens are valid: true, expires at: 1748773758857
[debug] [2025-06-01T09:29:34.851Z] Checked if tokens are valid: true, expires at: 1748773758857
[debug] [2025-06-01T09:29:34.851Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/whatsappintegration-ae33b [none]
[debug] [2025-06-01T09:29:35.796Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/whatsappintegration-ae33b 200
[debug] [2025-06-01T09:29:35.797Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/whatsappintegration-ae33b {"projectId":"whatsappintegration-ae33b","projectNumber":"134640657599","displayName":"whatsappIntegration","name":"projects/whatsappintegration-ae33b","resources":{"hostingSite":"whatsappintegration-ae33b"},"state":"ACTIVE","etag":"1_56576fee-ce02-4954-a6fb-5682952aa7f4"}
[info] 
[info] Your public directory is the folder (relative to your project directory) that
[info] will contain Hosting assets to be uploaded with firebase deploy. If you
[info] have a build process for your assets, use your build's output directory.
[info] 
[debug] [2025-06-01T09:30:03.286Z] >>> [apiv2][query] GET https://www.gstatic.com/firebasejs/releases.json [none]
[debug] [2025-06-01T09:30:03.575Z] <<< [apiv2][status] GET https://www.gstatic.com/firebasejs/releases.json 200
[debug] [2025-06-01T09:30:03.575Z] <<< [apiv2][body] GET https://www.gstatic.com/firebasejs/releases.json {"current":{"version":"11.8.1","browserURL":"https://www.gstatic.com/firebasejs/11.8.1/firebase.js","packageURL":"https://www.gstatic.com/firebasejs/11.8.1/firebase.tgz"},"live":{"version":"11.8","browserURL":"https://www.gstatic.com/firebasejs/live/11.8/firebase.js","packageURL":"https://www.gstatic.com/firebasejs/live/11.8/firebase.tgz"},"components":{"ai":"https://www.gstatic.com/firebasejs/11.8.1/firebase-ai.js","analytics":"https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js","app":"https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js","app-check":"https://www.gstatic.com/firebasejs/11.8.1/firebase-app-check.js","auth":"https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js","auth/cordova":"https://www.gstatic.com/firebasejs/11.8.1/firebase-auth/cordova.js","auth/web-extension":"https://www.gstatic.com/firebasejs/11.8.1/firebase-auth/web-extension.js","functions":"https://www.gstatic.com/firebasejs/11.8.1/firebase-functions.js","firestore":"https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js","firestore/lite":"https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore/lite.js","installations":"https://www.gstatic.com/firebasejs/11.8.1/firebase-installations.js","storage":"https://www.gstatic.com/firebasejs/11.8.1/firebase-storage.js","performance":"https://www.gstatic.com/firebasejs/11.8.1/firebase-performance.js","remote-config":"https://www.gstatic.com/firebasejs/11.8.1/firebase-remote-config.js","messaging":"https://www.gstatic.com/firebasejs/11.8.1/firebase-messaging.js","messaging/sw":"https://www.gstatic.com/firebasejs/11.8.1/firebase-messaging/sw.js","database":"https://www.gstatic.com/firebasejs/11.8.1/firebase-database.js","vertexai":"https://www.gstatic.com/firebasejs/11.8.1/firebase-vertexai.js","data-connect":"https://www.gstatic.com/firebasejs/11.8.1/firebase-data-connect.js","firestore.memory":"https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.memory.js"}}
[info] ✔  Wrote build/web/index.html 
[info] 
[info] i  Detected a .git folder at /Users/<USER>/Downloads/Flutter_projects/whatsappIntegration 
[info] i  Authorizing with GitHub to upload your service account to a GitHub repository's secrets store. 
[info] 
[info] Visit this URL on this device to log in:
[info] https://github.com/login/oauth/authorize?client_id=89cf50f02ac6aaed3484&state=*********&redirect_uri=http%3A%2F%2Flocalhost%3A9005&scope=read%3Auser%20repo%20public_repo
[info] 
[info] Waiting for authentication...
[debug] [2025-06-01T09:30:04.595Z] >>> [apiv2][query] POST https://github.com/login/oauth/access_token [none]
[debug] [2025-06-01T09:30:04.596Z] >>> [apiv2][body] POST https://github.com/login/oauth/access_token [stream]
[debug] [2025-06-01T09:30:05.158Z] <<< [apiv2][status] POST https://github.com/login/oauth/access_token 200
[debug] [2025-06-01T09:30:05.158Z] <<< [apiv2][body] POST https://github.com/login/oauth/access_token {"access_token":"****************************************","token_type":"bearer","scope":"read:user,repo"}
[debug] [2025-06-01T09:30:05.165Z] >>> [apiv2][query] GET https://api.github.com/user [none]
[debug] [2025-06-01T09:30:05.747Z] <<< [apiv2][status] GET https://api.github.com/user 200
[debug] [2025-06-01T09:30:05.747Z] <<< [apiv2][body] GET https://api.github.com/user {"login":"singhrishabh93","id":92676834,"node_id":"U_kgDOBYYi4g","avatar_url":"https://avatars.githubusercontent.com/u/92676834?v=4","gravatar_id":"","url":"https://api.github.com/users/singhrishabh93","html_url":"https://github.com/singhrishabh93","followers_url":"https://api.github.com/users/singhrishabh93/followers","following_url":"https://api.github.com/users/singhrishabh93/following{/other_user}","gists_url":"https://api.github.com/users/singhrishabh93/gists{/gist_id}","starred_url":"https://api.github.com/users/singhrishabh93/starred{/owner}{/repo}","subscriptions_url":"https://api.github.com/users/singhrishabh93/subscriptions","organizations_url":"https://api.github.com/users/singhrishabh93/orgs","repos_url":"https://api.github.com/users/singhrishabh93/repos","events_url":"https://api.github.com/users/singhrishabh93/events{/privacy}","received_events_url":"https://api.github.com/users/singhrishabh93/received_events","type":"User","user_view_type":"private","site_admin":false,"name":"Rishabh Singh","company":"Monova","blog":"rishabh16.me","location":null,"email":"<EMAIL>","hireable":true,"bio":"Mobile Engineer | iOS & Android","twitter_username":"singhrishabh93","notification_email":"<EMAIL>","public_repos":177,"public_gists":0,"followers":39,"following":27,"created_at":"2021-10-17T12:59:32Z","updated_at":"2025-05-22T12:32:08Z","private_gists":2,"total_private_repos":21,"owned_private_repos":19,"disk_usage":1519620,"collaborators":2,"two_factor_authentication":true,"plan":{"name":"pro","space":*********,"collaborators":0,"private_repos":9999}}
[info] 
[info] ✔  Success! Logged into GitHub as singhrishabh93 
[info] 
[debug] [2025-06-01T09:30:13.892Z] >>> [apiv2][query] GET https://api.github.com/repos/y/actions/secrets/public-key type=owner
[debug] [2025-06-01T09:30:14.531Z] <<< [apiv2][status] GET https://api.github.com/repos/y/actions/secrets/public-key 404
[debug] [2025-06-01T09:30:14.532Z] <<< [apiv2][body] GET https://api.github.com/repos/y/actions/secrets/public-key {"message":"Not Found","documentation_url":"https://docs.github.com/rest","status":"404"}
[info] 
[info] 
[error] ⚠  The provided authorization cannot be used with this repository. If this repository is in an organization, did you remember to grant access? 
[info] 
[info] i  Action required: Visit this URL to ensure access has been granted to the appropriate organization(s) for the Firebase CLI GitHub OAuth App: 
[info] https://github.com/settings/connections/applications/89cf50f02ac6aaed3484
[info] 
[debug] [2025-06-01T09:30:21.943Z] >>> [apiv2][query] GET https://api.github.com/repos/y/actions/secrets/public-key type=owner
[debug] [2025-06-01T09:30:22.519Z] <<< [apiv2][status] GET https://api.github.com/repos/y/actions/secrets/public-key 404
[debug] [2025-06-01T09:30:22.519Z] <<< [apiv2][body] GET https://api.github.com/repos/y/actions/secrets/public-key {"message":"Not Found","documentation_url":"https://docs.github.com/rest","status":"404"}
[info] 
[info] 
[error] ⚠  The provided authorization cannot be used with this repository. If this repository is in an organization, did you remember to grant access? 
[info] 
[info] i  Action required: Visit this URL to ensure access has been granted to the appropriate organization(s) for the Firebase CLI GitHub OAuth App: 
[info] https://github.com/settings/connections/applications/89cf50f02ac6aaed3484
[info] 
[debug] [2025-06-01T09:30:22.533Z] >>> [apiv2][query] GET https://api.github.com/repos/y/actions/secrets/public-key type=owner
[debug] [2025-06-01T09:30:23.131Z] <<< [apiv2][status] GET https://api.github.com/repos/y/actions/secrets/public-key 404
[debug] [2025-06-01T09:30:23.131Z] <<< [apiv2][body] GET https://api.github.com/repos/y/actions/secrets/public-key {"message":"Not Found","documentation_url":"https://docs.github.com/rest","status":"404"}
[info] 
[info] 
[error] ⚠  The provided authorization cannot be used with this repository. If this repository is in an organization, did you remember to grant access? 
[info] 
[info] i  Action required: Visit this URL to ensure access has been granted to the appropriate organization(s) for the Firebase CLI GitHub OAuth App: 
[info] https://github.com/settings/connections/applications/89cf50f02ac6aaed3484
[info] 
[debug] [2025-06-01T09:30:33.743Z] >>> [apiv2][query] GET https://api.github.com/repos//actions/secrets/public-key type=owner
[debug] [2025-06-01T09:30:34.501Z] <<< [apiv2][status] GET https://api.github.com/repos//actions/secrets/public-key 404
[debug] [2025-06-01T09:30:34.501Z] <<< [apiv2][body] GET https://api.github.com/repos//actions/secrets/public-key {"message":"Not Found","documentation_url":"https://docs.github.com/rest","status":"404"}
[info] 
[info] 
[error] ⚠  The provided authorization cannot be used with this repository. If this repository is in an organization, did you remember to grant access? 
[info] 
[info] i  Action required: Visit this URL to ensure access has been granted to the appropriate organization(s) for the Firebase CLI GitHub OAuth App: 
[info] https://github.com/settings/connections/applications/89cf50f02ac6aaed3484
[info] 
[debug] [2025-06-01T09:31:46.527Z] >>> [apiv2][query] GET https://api.github.com/repos/monova-ai/whatsappIntegration/actions/secrets/public-key type=owner
[debug] [2025-06-01T09:31:47.309Z] <<< [apiv2][status] GET https://api.github.com/repos/monova-ai/whatsappIntegration/actions/secrets/public-key 404
[debug] [2025-06-01T09:31:47.309Z] <<< [apiv2][body] GET https://api.github.com/repos/monova-ai/whatsappIntegration/actions/secrets/public-key {"message":"Not Found","documentation_url":"https://docs.github.com/rest/actions/secrets#get-a-repository-public-key","status":"404"}
[info] 
[info] 
[error] ⚠  The provided authorization cannot be used with this repository. If this repository is in an organization, did you remember to grant access? 
[info] 
[info] i  Action required: Visit this URL to ensure access has been granted to the appropriate organization(s) for the Firebase CLI GitHub OAuth App: 
[info] https://github.com/settings/connections/applications/89cf50f02ac6aaed3484
[info] 
