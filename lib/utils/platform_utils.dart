import 'package:flutter/foundation.dart';

class PlatformUtils {
  /// Returns true if running on web platform
  static bool get isWeb => kIsWeb;
  
  /// Returns true if running on mobile (iOS or Android)
  static bool get isMobile => !kIsWeb;
  
  /// Returns true if the screen should be constrained to mobile dimensions
  static bool shouldConstrainToMobile(double screenWidth) {
    return isWeb && screenWidth > 500;
  }
  
  /// Standard mobile widths for different devices
  static const double iPhoneSEWidth = 375.0;
  static const double iPhoneStandardWidth = 390.0;
  static const double iPhoneProMaxWidth = 428.0;
  
  /// Get appropriate mobile width based on preference
  static double getMobileWidth({String size = 'standard'}) {
    switch (size) {
      case 'small':
        return iPhoneSEWidth;
      case 'large':
        return iPhoneProMaxWidth;
      case 'standard':
      default:
        return iPhoneStandardWidth;
    }
  }
}
