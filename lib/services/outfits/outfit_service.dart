import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/services/weatherservice.dart';

// Common Outfit model
class Outfit {
  final String userId;
  final String outfitId;
  final String outfitName;
  final List<String> apparelPointers;
  final String dateCreated;
  final String status;
  final List<String> tags;
  List<Apparel> apparels = [];
  String description;

  Outfit({
    required this.userId,
    required this.outfitId,
    required this.outfitName,
    required this.apparelPointers,
    required this.dateCreated,
    required this.status,
    required this.tags,
    this.description = '',
  });

  factory Outfit.fromJson(Map<String, dynamic> json) {
    return Outfit(
      userId: json['userId'],
      outfitId: json['outfitId'],
      outfitName: json['outfitName'],
      apparelPointers: List<String>.from(json['apparelPointers']),
      dateCreated: json['dateCreated'] ?? DateTime.now().toIso8601String(),
      status: json['status'] ?? 'PRIMARY',
      tags: List<String>.from(json['tags']),
    );
  }
}

// Common Apparel model
class Apparel {
  final String apparelId;
  final String apparelType;
  final String apparelMediaUrl;
  final String productName;
  final String brand;
  final String colour;

  Apparel({
    required this.apparelId,
    required this.apparelType,
    required this.apparelMediaUrl,
    required this.productName,
    required this.brand,
    required this.colour,
  });

  factory Apparel.fromJson(Map<String, dynamic> json) {
    return Apparel(
      apparelId: json['data']['apparelId'],
      apparelType: json['data']['apparelType'],
      apparelMediaUrl: json['data']['apparelMediaUrl'],
      productName: json['data']['productName'],
      brand: json['data']['brand'],
      colour: json['data']['colour'],
    );
  }
}

// Collection model
class OutfitCollection {
  final String userId;
  final String outfitCollectionId;
  final String outfitCollectionName;
  final String outfitCollectionMediaUrl;
  final List<String> outfitPointers;
  final String dateCreated;

  OutfitCollection({
    required this.userId,
    required this.outfitCollectionId,
    required this.outfitCollectionName,
    required this.outfitCollectionMediaUrl,
    this.outfitPointers = const [],
    this.dateCreated = '',
  });

  factory OutfitCollection.fromJson(Map<String, dynamic> json) {
    return OutfitCollection(
      userId: json['userId'],
      outfitCollectionId: json['outfitCollectionId'],
      outfitCollectionName: json['outfitCollectionName'],
      outfitCollectionMediaUrl: json['outfitCollectionMediaUrl'] ?? '',
      outfitPointers: json['outfitPointers'] != null
          ? List<String>.from(json['outfitPointers'] is String
              ? [json['outfitPointers']]
              : json['outfitPointers'])
          : [],
      dateCreated: json['dateCreated'] ?? '',
    );
  }
}

class OutfitService {
  // Fetch outfits based on weather
  static Future<List<Outfit>> fetchTodayOutfits() async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return [];
      }

      // Get weather data to pass to the API
      final weatherData = await WeatherLocationService.getWeatherData();
      String weatherCondition = weatherData.condition;

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/get-today-outfits?userId=$userId&weather=$weatherCondition'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        List<Outfit> fetchedOutfits = [];
        if (jsonResponse['data'] != null) {
          Map<String, dynamic> outfitsByCategory = jsonResponse['data'];
          outfitsByCategory.forEach((category, outfitData) {
            if (outfitData != null && outfitData is Map<String, dynamic>) {
              Outfit outfit = Outfit(
                userId: outfitData['userId'],
                outfitId: outfitData['outfitId'],
                outfitName: outfitData['outfitName'],
                apparelPointers: List<String>.from(outfitData['apparelPointers']),
                dateCreated: outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
                status: outfitData['status'] ?? 'PRIMARY',
                tags: List<String>.from(outfitData['tags']),
                description: 'Category: ${category.toUpperCase()}',
              );
              fetchedOutfits.add(outfit);
            }
          });
        }

        // Fetch apparel details for each outfit
        for (var outfit in fetchedOutfits) {
          if (outfit.apparelPointers.isNotEmpty) {
            await fetchApparelDetails(outfit);
          }
        }

        return fetchedOutfits;
      } else {
        print("Error fetching today's outfits: ${response.reasonPhrase}");
        return [];
      }
    } catch (e) {
      print("Exception when fetching today's outfits: $e");
      return [];
    }
  }

  // Fetch apparel details for an outfit
  static Future<void> fetchApparelDetails(Outfit outfit) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      // For better performance, only fetch the first four apparels
      for (int i = 0; i < outfit.apparelPointers.length && i < 4; i++) {
        if (outfit.apparelPointers[i].isEmpty) {
          continue;
        }

        var headers = {'Authorization': 'Bearer $authToken'};

        // Modified URL to include userId as query parameter
        var request = http.Request(
            'GET',
            Uri.parse(
                '${ApiConstants.apparelApi}apparel/${outfit.apparelPointers[i]}?userId=$userId'));
        request.headers.addAll(headers);

        try {
          http.StreamedResponse response = await request.send();

          if (response.statusCode == 200) {
            String responseBody = await response.stream.bytesToString();
            Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

            if (jsonResponse['data'] != null) {
              Apparel apparel = Apparel.fromJson(jsonResponse);
              outfit.apparels.add(apparel);
            }
          } else {
            print("Info: Apparel ${outfit.apparelPointers[i]} not found or not accessible: ${response.reasonPhrase}");
          }
        } catch (e) {
          print("Info: Exception fetching apparel ${outfit.apparelPointers[i]}: $e");
          // Continue to next apparel even if one fails
        }
      }
    } catch (e) {
      print("Exception when fetching apparel details: $e");
    }
  }

  // Fetch collections
  static Future<List<OutfitCollection>> fetchCollections() async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return [];
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/list?userId=$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        List<OutfitCollection> fetchedCollections = [];
        if (jsonResponse['data'] != null) {
          for (var collectionJson in jsonResponse['data']) {
            OutfitCollection collection = OutfitCollection.fromJson(collectionJson);
            fetchedCollections.add(collection);
          }
        }

        return fetchedCollections;
      } else {
        print("Error fetching collections: ${response.reasonPhrase}");
        return [];
      }
    } catch (e) {
      print("Exception when fetching collections: $e");
      return [];
    }
  }

  // Create a new collection
  static Future<bool> createCollection(String collectionName, [Outfit? outfit]) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return false;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'POST',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections?userId=$userId'));

      // Create the request body based on whether an outfit is provided
      var requestBody = {
        "outfitCollectionName": collectionName,
        "outfitPointers": outfit != null ? [outfit.outfitId] : []
      };

      request.body = jsonEncode(requestBody);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 201) {
        String responseBody = await response.stream.bytesToString();
        print("Collection created successfully: $responseBody");
        return true;
      } else {
        print("Error creating collection: ${response.reasonPhrase}");
        return false;
      }
    } catch (e) {
      print("Exception when creating collection: $e");
      return false;
    }
  }

  // Save outfit to collection
  static Future<bool> saveOutfitToCollection(OutfitCollection collection, Outfit outfit) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return false;
      }

      // Check if outfit is already in the collection
      if (collection.outfitPointers.contains(outfit.outfitId)) {
        print("Outfit already in collection");
        return false;
      }

      // Update collection with new outfit
      var collectionHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      // Create a new list with the current outfit added
      List<String> updatedOutfitPointers = [
        ...collection.outfitPointers,
        outfit.outfitId
      ];

      var collectionRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/${collection.outfitCollectionId}?userId=$userId'));

      collectionRequest.body = jsonEncode({
        "outfitCollectionName": collection.outfitCollectionName,
        "outfitPointers": updatedOutfitPointers
      });

      collectionRequest.headers.addAll(collectionHeaders);

      http.StreamedResponse collectionResponse = await collectionRequest.send();

      // Update outfit status
      var outfitHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var outfitRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/outfits/${outfit.outfitId}?userId=$userId'));

      outfitRequest.body = jsonEncode({
        "status": "WISHLIST",
        "outfitCollectionId": collection.outfitCollectionId
      });

      outfitRequest.headers.addAll(outfitHeaders);

      http.StreamedResponse outfitResponse = await outfitRequest.send();

      if (collectionResponse.statusCode == 200 && outfitResponse.statusCode == 200) {
        String collectionResponseBody = await collectionResponse.stream.bytesToString();
        String outfitResponseBody = await outfitResponse.stream.bytesToString();
        print("Collection updated successfully: $collectionResponseBody");
        print("Outfit updated successfully: $outfitResponseBody");
        return true;
      } else {
        print("Error updating collection: ${collectionResponse.reasonPhrase}");
        print("Error updating outfit: ${outfitResponse.reasonPhrase}");
        return false;
      }
    } catch (e) {
      print("Exception when saving outfit to collection: $e");
      return false;
    }
  }

  // Fetch user gender
  static Future<String?> fetchUserGender() async {
    try {
      final userId = await SharedPrefsService.getUserId();
      if (userId == null) return null;

      final authToken = await SharedPrefsService.getAuthToken();
      if (authToken == null) return null;

      final response = await http.get(
        Uri.parse('${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final userData = jsonResponse['data'];

        if (userData != null && userData['userGender'] != null) {
          return userData['userGender'];
        }
      }
      return null;
    } catch (e) {
      print('Error fetching user gender: $e');
      return null;
    }
  }

  // Get icon for collection based on name
  static IconData getIconForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return Icons.work_outline;
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return Icons.favorite;
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return Icons.wb_sunny_outlined;
    }
    return Icons.style;
  }

  // Get color for collection based on name
  static Color getColorForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return const Color(0xFFFFF8E7);
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return const Color(0xFFFFF1F1);
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return const Color(0xFFF5F1FF);
    }
    return Colors.white;
  }

  // Get gradient colors for collection based on name
  static List<Color> getGradientColorsForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return [const Color(0xFFFFF8E7), const Color(0xFFFFE5C4)];
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return [const Color(0xFFFFF1F1), const Color(0xFFFFD6D6)];
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return [const Color(0xFFF5F1FF), const Color(0xFFE8E0FF)];
    }
    // Default gradient
    return [const Color(0xFFE3F2FD), const Color(0xFFBBDEFB)];
  }

  // Build colorful tags for outfits
  static List<Widget> buildColorfulTags(List<String> tags) {
    // Map of tag colors
    final Map<String, Color> tagColors = {
      'Casual': const Color(0xFFFCE4EC),
      'Night Out': const Color(0xFFFFF8E1),
      'Date Night': const Color(0xFFF1F8E9),
      'Workwear': const Color(0xFFE0F7FA),
      'Semi-formal': const Color(0xFFE8EAF6),
      'Trendy': const Color(0xFFF3E5F5),
      'Office': const Color(0xFFE0F2F1),
      'Formal': const Color(0xFFE3F2FD),
      'Classic': const Color(0xFFE8F5E9),
      'Weekend': const Color(0xFFEFEBE9),
    };

    return tags.map((tag) {
      // Use the predefined color or default to a light gray
      final Color tagColor = tagColors[tag] ?? const Color(0xFFEEEEEE);

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tagColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          tag,
          style: const TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 12,
            color: Colors.black87,
          ),
        ),
      );
    }).toList();
  }

  // Build outfit images grid
  static Widget buildOutfitImagesGrid(Outfit outfit) {
    if (outfit.apparels.isEmpty) {
      return Container(
        height: 245,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(Icons.broken_image),
      );
    } else if (outfit.apparels.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          outfit.apparels[0].apparelMediaUrl,
          height: 245,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Container(
            height: 245,
            color: Colors.grey[300],
            child: const Icon(Icons.broken_image),
          ),
        ),
      );
    } else if (outfit.apparels.length == 2) {
      return Column(
        children: [
          // Top image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfit.apparels[0].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          // Bottom image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfit.apparels[1].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
        ],
      );
    } else if (outfit.apparels.length == 3) {
      return Column(
        children: [
          // Top image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfit.apparels[0].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          // Bottom row with two images
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[1].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[2].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[0].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[1].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[2].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[3].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  // Build empty outfit card (when no outfits available)
  static Widget buildEmptyOutfitCard(BuildContext context, String? userGender) {
    final String imagePath;
    if (userGender == null) {
      imagePath = 'assets/cardImages/women_recomm.png'; // Default image
    } else if (userGender == 'MASCULINE') {
      imagePath = 'assets/cardImages/men_recomm.png';
    } else {
      imagePath = 'assets/cardImages/women_recomm.png';
    }

    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              imagePath, // Gender-based image
              height: 120,
              width: 120,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 24),
            const Text(
              'Fresh New Fits Everyday!',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              "That's it for today!",
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Come tomorrow to see some\nfresh new fits.',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Build no cards widget (when wardrobe is empty)
  static Widget buildNoCardsWidget() {
    return Center(
      child: Container(
        height: 502,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/emptyImages/almirah.png', 
              width: 300,
              height: 300,
            ),
            const SizedBox(height: 16),
            const Text(
              "Your wardrobe is empty!",
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                "Add 6 pieces to start—2 tops, 2 bottoms & 2 shoes.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build shimmer loading outfit card
  static Widget buildShimmerOutfitCard(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: LinearProgressIndicator(
              value: null, // Indeterminate progress
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF76037)),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icons/star.png', // Using star from assets
                  width: 14,
                  height: 14,
                ),
                const SizedBox(width: 8),
                Text(
                  'Understanding your style...',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build style journal card for collections grid
  static Widget buildStyleJournalCard(OutfitCollection collection) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: getGradientColorsForCollection(collection.outfitCollectionName),
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // Image circle for collection image
          Positioned(
            top: 12,
            right: 12,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: collection.outfitCollectionMediaUrl.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: Image.network(
                          collection.outfitCollectionMediaUrl,
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            getIconForCollection(collection.outfitCollectionName),
                            size: 24,
                            color: Colors.black,
                          ),
                        ),
                      )
                    : Icon(
                        getIconForCollection(collection.outfitCollectionName),
                        size: 24,
                        color: Colors.black,
                      ),
              ),
            ),
          ),
          // Title text
          Positioned(
            bottom: 16,
            left: 16,
            child: Text(
              collection.outfitCollectionName,
              style: const TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 14,
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build save option row for collection selection bottom sheet
  static Widget buildSaveOptionRow({
    required String title,
    required int outfitCount,
    required VoidCallback onTap,
    bool isNew = false,
    OutfitCollection? collection,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 83,
            height: 83,
            decoration: BoxDecoration(
              color: isNew ? Colors.white : getColorForCollection(title),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: isNew
                  ? const Icon(Icons.add, color: Colors.black, size: 24)
                  : (collection != null &&
                          collection.outfitCollectionMediaUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Image.network(
                            collection.outfitCollectionMediaUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              getIconForCollection(title),
                              size: 24,
                              color: Colors.black,
                            ),
                          ),
                        )
                      : Icon(getIconForCollection(title),
                          size: 24, color: Colors.black)),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$outfitCount outfits',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            backgroundColor: Colors.grey[200],
            child: IconButton(
              icon: const Icon(Icons.add, color: Colors.grey),
              onPressed: onTap,
            ),
          ),
        ],
      ),
    );
  }

  // Show save outfit bottom sheet
  static void showSaveOutfitBottomSheet(BuildContext context, Outfit outfit) async {
    try {
      // Fetch collections before showing the bottom sheet
      List<OutfitCollection> collections = await fetchCollections();
      
      showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Save to',
                        style: TextStyle(
                          fontFamily: 'SatoshiM',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 24),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          buildSaveOptionRow(
                            title: 'New Style Group',
                            outfitCount: 0,
                            isNew: true,
                            onTap: () {
                              showNewStyleGroupModal(context, outfit);
                            },
                          ),
                          ...collections.map((collection) {
                            return buildSaveOptionRow(
                              title: collection.outfitCollectionName,
                              outfitCount: collection.outfitPointers.length,
                              collection: collection,
                              onTap: () async {
                                bool success = await saveOutfitToCollection(collection, outfit);
                                if (success) {
                                  Navigator.pop(context);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Outfit saved to ${collection.outfitCollectionName}'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                } else {
                                  if (collection.outfitPointers.contains(outfit.outfitId)) {
                                    Navigator.pop(context);
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Outfit already in ${collection.outfitCollectionName}'),
                                        backgroundColor: Colors.orange,
                                      ),
                                    );
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Failed to save outfit to collection'),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                }
                              },
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            );
          });
        },
      );
    } catch (e) {
      print("Error showing save outfit bottom sheet: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Show new style group modal
  static void showNewStyleGroupModal(BuildContext context, [Outfit? outfit]) {
    // Create a local controller that won't be disposed with the screen
    TextEditingController localController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'New Style Group',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 24),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  height: 200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.help_outline,
                            color: Colors.grey,
                            size: 50,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'The image is auto generated\nbased on the name',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Journal Name',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: localController,
                  decoration: InputDecoration(
                    hintText: 'Party at Night, Fun day out...',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      if (localController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Please enter a name for your style group'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // Store the value before closing modal
                      final collectionName = localController.text.trim();

                      // Close this modal
                      Navigator.pop(context);

                      // Create collection with or without outfit
                      bool success = await createCollection(collectionName, outfit);
                      
                      if (success) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Collection created successfully'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to create collection'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xffF76037),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}