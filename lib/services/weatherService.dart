// lib/services/weather_location_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WeatherLocationData {
  final String temperature;
  final String location;
  final String condition; // Add this property


  WeatherLocationData({
    required this.temperature,
    required this.location,
    required this.condition, // Add this parameter
  });
}

class WeatherLocationService {
  static const String _keyLastFetchTime = 'weather_last_fetch_time';
  static const String _keyTemperature = 'weather_temperature';
  static const String _keyLocation = 'weather_location';

  // Check if we need to refresh weather data
  // We'll refresh if there's no saved data or if the last fetch was more than 1 hour ago
  static Future<bool> _needsRefresh() async {
    final prefs = await SharedPreferences.getInstance();
    final lastFetchTime = prefs.getInt(_keyLastFetchTime);

    if (lastFetchTime == null) return true;

    final now = DateTime.now().millisecondsSinceEpoch;
    final oneHourInMillis = 60 * 60 * 1000;

    return (now - lastFetchTime) > oneHourInMillis;
  }

  // Save weather data
  static Future<void> _saveWeatherData({
    required String temperature,
    required String location,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyTemperature, temperature);
    await prefs.setString(_keyLocation, location);
    await prefs.setInt(
        _keyLastFetchTime, DateTime.now().millisecondsSinceEpoch);
  }

  // Get cached weather data
  static Future<WeatherLocationData?> _getCachedWeatherData() async {
    final prefs = await SharedPreferences.getInstance();
    final temperature = prefs.getString(_keyTemperature);
    final location = prefs.getString(_keyLocation);
    final condition = prefs.getString('weather_condition') ?? '';

    

    if (temperature != null && location != null) {
      return WeatherLocationData(
        temperature: temperature,
        location: location,
        condition: condition,  // Use the cached condition value
      );
    }

    return null;
  }

  // Get current position
  static Future<Position> _determinePosition() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled.');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition();
  }

  // Get weather data from API
  static Future<Map<String, dynamic>> _getWeatherData(
      double lat, double lon) async {
    final apiKey = '09a01538920695300d4f4501b9fc9297';
    final url =
        'https://api.openweathermap.org/data/2.5/weather?lat=$lat&lon=$lon&units=metric&appid=$apiKey';

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return {
        'temp': data['main']['temp'],
        'city': data['name'],
        'condition': data['weather'][0]['main'], // Get the weather condition
      };
    } else {
      throw Exception('Failed to load weather data');
    }
  }

  // Main method to get weather data
  static Future<WeatherLocationData> getWeatherData() async {
  try {
    // Get current position
    final position = await _determinePosition();
    
    // Fetch weather data from API
    final weatherData = await _getWeatherData(position.latitude, position.longitude);

    // Extract data
    final temperature = '${weatherData['temp'].round()}°C';
    final location = weatherData['city'] ?? 'IN';
    final condition = weatherData['condition'] ?? '';

    // Return data directly without saving
    return WeatherLocationData(
      temperature: temperature,
      location: location,
      condition: condition,
    );
  } catch (e) {
    // Return default values if there's an error
    return WeatherLocationData(
      temperature: '22°C',
      location: 'Ch, IN',
      condition: 'Clear', // Default condition
    );
  }
}
}
