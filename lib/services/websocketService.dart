import 'dart:async';
import 'dart:convert';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/services/sharedPrefsService.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:http/http.dart' as http;

class WebSocketService {
  static WebSocketService? _instance;
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _messageController;
  int _reconnectAttempts = 0;
  static const int MAX_RECONNECT_ATTEMPTS = 3;
  bool _isConnecting = false;
  Timer? _pingTimer;

  WebSocketService._(); // Private constructor

  static WebSocketService get instance {
    _instance ??= WebSocketService._();
    return _instance!;
  }

  bool _intentionalDisconnect = false;
  DateTime? _lastConnectionAttempt;

  Future<void> connect() async {
    // Prevent multiple simultaneous connection attempts
    if (_isConnecting) return;
    _isConnecting = true;

    // Add connection throttling
    final now = DateTime.now();
    if (_lastConnectionAttempt != null &&
        now.difference(_lastConnectionAttempt!).inSeconds < 5) {
      print('Connection attempts too frequent, delaying...');
      await Future.delayed(const Duration(seconds: 5));
    }
    _lastConnectionAttempt = now;

    _intentionalDisconnect = false;

    if (_reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      print('Max reconnection attempts reached');
      _reconnectAttempts = 0;
      _isConnecting = false;
      return;
    }

    try {
      if (_channel != null) {
        await _channel!.sink.close();
        _channel = null;
      }

      final userId = await SharedPrefsService.getUserId();
      if (userId == null) throw Exception('UserId not found');

      final fullUserId = userId.startsWith('91') ? userId : '91$userId';
      final authToken = await SharedPrefsService.getAuthToken();

      final wsUrl =
          '${ApiConstants.chatSocketUrl}?userId=$fullUserId&authToken=$authToken&message=${Uri.encodeComponent(jsonEncode({
            "userId": fullUserId,
            "authToken": authToken
          }))}';

      print('Connecting to WebSocket: $wsUrl');
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      _messageController = StreamController<Map<String, dynamic>>.broadcast();

      // Send initial connection message to confirm connection
      _channel!.sink
          .add(json.encode({"action": "connect", "type": "connection_init"}));

      _channel!.stream.listen(
        (message) {
          print('Raw WebSocket message received: $message');
          try {
            final decodedMessage = json.decode(message);
            if (decodedMessage['message'] == 'Forbidden') {
              // print('Authorization failed');
              return;
            }

            // If we get a connection acknowledge message, mark as stable
            if (decodedMessage['type'] == 'connection_ack' ||
                decodedMessage['message'] == 'Connected') {
              print('Connection acknowledged by server');
            }

            _messageController!.add(decodedMessage);
          } catch (e) {
            print('Error decoding message: $e');
          }
        },
        onError: (error) {
          print('WebSocket Error: $error');
          _reconnectAttempts++;
          _isConnecting = false;
          if (!_intentionalDisconnect) {
            reconnect();
          }
        },
        onDone: () {
          print('WebSocket connection closed');
          // Only attempt reconnect if not intentionally disconnected
          if (_channel != null && !_intentionalDisconnect) {
            _reconnectAttempts++;
            _isConnecting = false;
            reconnect();
          } else {
            print('Intentional disconnect, not reconnecting');
          }
        },
      );

      // Start a ping timer to keep the connection alive
      _startPingTimer();

      print('WebSocket connection established');
      _reconnectAttempts =
          0; // Reset reconnect attempts on successful connection
      _isConnecting = false;
    } catch (e) {
      print('Connection error: $e');
      _reconnectAttempts++;
      _isConnecting = false;
      await Future.delayed(const Duration(seconds: 2));
      connect();
    }
  }

  void _startPingTimer() {
    _pingTimer?.cancel();
    // AWS API Gateway WebSockets disconnect after ~10 minutes of inactivity
    // Send a ping every 5 minutes to keep the connection alive
    _pingTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      if (_channel != null && _channel!.sink != null) {
        try {
          print('Sending ping to keep connection alive');
          _channel!.sink.add(json.encode({"action": "ping"}));
        } catch (e) {
          print('Error sending ping: $e');
        }
      }
    });
  }

  void reconnect() {
    if (_reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      print('Attempting reconnect, attempt $_reconnectAttempts');
      // Add exponential backoff for reconnection attempts
      Future.delayed(Duration(seconds: _reconnectAttempts * 2), () {
        connect();
      });
    } else {
      print('Max reconnection attempts reached');
      _reconnectAttempts = 0;
    }
  }

  void disconnect() {
    _pingTimer?.cancel();
    _pingTimer = null;
    _channel?.sink.close();
    _messageController?.close();
    _channel = null;
    _messageController = null;
    print('WebSocket disconnected');
  }

  Future<Map<String, dynamic>> fetchApparelDetails(String apparelId) async {
  try {
    final userId = await SharedPrefsService.getUserId();
    final authToken = await SharedPrefsService.getAuthToken();
    
    final uri = Uri.parse('${ApiConstants.apparelApi}/apparel/$apparelId?userId=$userId');
    
    final response = await http.get(
      uri,
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json',
      },
    );
    
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      print('Error fetching apparel details: ${response.reasonPhrase}');
      return {};
    }
  } catch (e) {
    print('Exception fetching apparel details: $e');
    return {};
  }
}

  Future<void> sendMessage(String messageContent) async {
    if (_channel == null) {
      print(
          'WebSocket not connected, attempting to connect before sending message');
      await connect();
      // Add a small delay to ensure connection is established
      await Future.delayed(const Duration(milliseconds: 500));
    }

    final userId = await SharedPrefsService.getUserId();
    final fullName = await SharedPrefsService.getFullName();

    print('Sending message with userId: $userId, fullName: $fullName');

    final message = {
      "action": "inbound",
      "object": "chatapp_account",
      "entry": [
        {
          "id": "***************",
          "changes": [
            {
              "value": {
                "messaging_product": "chatapp",
                "metadata": {
                  "display_phone_number": "***********",
                  "phone_number_id": "***************"
                },
                "contacts": [
                  {
                    "profile": {"name": fullName ?? "User"},
                    "wa_id": userId
                  }
                ],
                "messages": [
                  {
                    "from": userId,
                    "id": "wamid.${DateTime.now().millisecondsSinceEpoch}",
                    "timestamp":
                        "${DateTime.now().millisecondsSinceEpoch ~/ 1000}",
                    "text": {"body": messageContent},
                    "type": "text"
                  }
                ]
              },
              "field": "messages"
            }
          ]
        }
      ]
    };

    try {
      print('Sending WebSocket message: ${json.encode(message)}');
      _channel?.sink.add(json.encode(message));
    } catch (e) {
      print('Error sending message: $e');
      // Try to reconnect and send again
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
      _channel?.sink.add(json.encode(message));
    }
  }

  void sendButtonReply(String buttonId, String buttonTitle) async {
    if (_channel == null) {
      print(
          'WebSocket not connected, attempting to connect before sending button reply');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    final userId = await SharedPrefsService.getUserId();
    final fullName = await SharedPrefsService.getFullName();

    print('Sending message with userId: $userId, fullName: $fullName');

    final message = {
      "action": "inbound",
      "object": "chatapp_account",
      "entry": [
        {
          "id": "***************",
          "changes": [
            {
              "value": {
                "messaging_product": "chatapp",
                "metadata": {
                  "display_phone_number": "***********",
                  "phone_number_id": "***************"
                },
                "contacts": [
                  {
                    "profile": {"name": fullName ?? "User"},
                    "wa_id": userId
                  }
                ],
                "messages": [
                  {
                    "from": userId,
                    "id": "wamid.${DateTime.now().millisecondsSinceEpoch}",
                    "timestamp":
                        "${DateTime.now().millisecondsSinceEpoch ~/ 1000}",
                    "type": "interactive",
                    "interactive": {
                      "type": "button_reply",
                      "button_reply": {"id": buttonId, "title": buttonTitle}
                    }
                  }
                ]
              },
              "field": "messages"
            }
          ]
        }
      ]
    };

    try {
      _channel?.sink.add(json.encode(message));
    } catch (e) {
      print('Error sending button reply: $e');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
      _channel?.sink.add(json.encode(message));
    }
  }

  void sendListReply(String id, String title) async {
    if (_channel == null) {
      print(
          'WebSocket not connected, attempting to connect before sending list reply');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    final userId = await SharedPrefsService.getUserId();
    final fullName = await SharedPrefsService.getFullName();

    final message = {
      "action": "inbound",
      "object": "chatapp_account",
      "entry": [
        {
          "id": "***************",
          "changes": [
            {
              "value": {
                "messaging_product": "chatapp",
                "metadata": {
                  "display_phone_number": "***********",
                  "phone_number_id": "***************"
                },
                "contacts": [
                  {
                    "profile": {"name": fullName ?? "User"},
                    "wa_id": userId
                  }
                ],
                "messages": [
                  {
                    "context": {
                      "from": "***********",
                      "id":
                          "wamid.${DateTime.now().millisecondsSinceEpoch}"
                    },
                    "from": userId,
                    "id": "wamid.${DateTime.now().millisecondsSinceEpoch}",
                    "timestamp":
                        "${DateTime.now().millisecondsSinceEpoch ~/ 1000}",
                    "type": "interactive",
                    "interactive": {
                      "type": "list_reply",
                      "list_reply": {"id": id, "title": title}
                    }
                  }
                ]
              },
              "field": "messages"
            }
          ]
        }
      ]
    };

    try {
      print('Sending list reply: ${json.encode(message)}');
      _channel?.sink.add(json.encode(message));
    } catch (e) {
      print('Error sending list reply: $e');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
      _channel?.sink.add(json.encode(message));
    }
  }

  Future<void> sendImageMessage(String imageUrl, String caption) async {
    if (_channel == null) {
      print(
          'WebSocket not connected, attempting to connect before sending image');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    final userId = await SharedPrefsService.getUserId();
    final fullName = await SharedPrefsService.getFullName();

    final message = {
      "action": "inbound",
      "object": "chatapp_account",
      "entry": [
        {
          "id": "***************",
          "changes": [
            {
              "value": {
                "messaging_product": "chatapp",
                "metadata": {
                  "display_phone_number": "***********",
                  "phone_number_id": "***************"
                },
                "contacts": [
                  {
                    "profile": {"name": fullName ?? "User"},
                    "wa_id": userId
                  }
                ],
                "messages": [
                  {
                    "from": userId,
                    "id": "wamid.${DateTime.now().millisecondsSinceEpoch}",
                    "timestamp":
                        "${DateTime.now().millisecondsSinceEpoch ~/ 1000}",
                    "image": {"url": imageUrl, "caption": caption},
                    "type": "image"
                  }
                ]
              },
              "field": "messages"
            }
          ]
        }
      ]
    };

    try {
      print('Sending image message: ${json.encode(message)}');
      _channel?.sink.add(json.encode(message));
    } catch (e) {
      print('Error sending image message: $e');
      await connect();
      await Future.delayed(const Duration(milliseconds: 500));
      _channel?.sink.add(json.encode(message));
    }
  }

  Stream<Map<String, dynamic>>? get messageStream => _messageController?.stream;

  bool get isConnected => _channel != null;
}