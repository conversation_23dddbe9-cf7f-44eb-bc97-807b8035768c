import 'package:flutter/material.dart';

class MobileViewportConfig {
  // Device dimensions
  static const double iPhoneSEWidth = 375.0;
  static const double iPhoneStandardWidth = 390.0;
  static const double iPhoneProMaxWidth = 428.0;
  
  // Default configuration
  static const double defaultMobileWidth = iPhoneStandardWidth;
  static const double defaultMobileHeight = 844.0; // iPhone 14 Pro height
  
  // Frame styling
  static const double frameRadius = 25.0;
  static const Color frameBackgroundColor = Color(0xFFF5F5F5);
  static const Color frameBorderColor = Color(0xFFE0E0E0);
  
  // Breakpoints
  static const double mobileBreakpoint = 500.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;
  
  // Shadow configuration
  static List<BoxShadow> get phoneShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 20,
      spreadRadius: 5,
      offset: const Offset(0, 10),
    ),
  ];
  
  static List<BoxShadow> get subtleShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 10,
      spreadRadius: 2,
      offset: const Offset(0, 5),
    ),
  ];
  
  // Helper methods
  static bool shouldShowMobileFrame(double screenWidth) {
    return screenWidth > mobileBreakpoint + 100;
  }
  
  static double getMobileWidth(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'small':
      case 'se':
        return iPhoneSEWidth;
      case 'large':
      case 'max':
        return iPhoneProMaxWidth;
      case 'standard':
      default:
        return iPhoneStandardWidth;
    }
  }
}
