import 'package:flutter/material.dart';

class AppTypography {
  // Heading styles
  static const TextStyle h1 = TextStyle(
    fontFamily: '<PERSON>shi<PERSON>',
    fontSize: 28.0,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle h2 = TextStyle(
    fontFamily: 'Satoshi<PERSON>',
    fontSize: 24.0,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle h3 = TextStyle(
    fontFamily: 'SatoshiB',
    fontSize: 20.0,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle h4 = TextStyle(
    fontFamily: 'SatoshiM',
    fontSize: 18.0,
    // fontWeight: FontWeight.w500,
  );

  static const TextStyle h5 = TextStyle(
    fontFamily: 'SatoshiM',
    fontSize: 16.0,
    fontWeight: FontWeight.w500,
  );

  // Body styles
  static const TextStyle body1 = TextStyle(
    fontFamily: '<PERSON>shi<PERSON>',
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle body2 = TextStyle(
    fontFamily: 'SatoshiM',
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
  );

  // Caption style
  static const TextStyle caption = TextStyle(
    fontFamily: 'SatoshiR',
    fontSize: 12.0,
    fontWeight: FontWeight.w500,
  );
}

//Import typography.dart and use with: style: AppTypography.h1
// Text(
//   'Your text here',
//   style: AppTypography.h1.copyWith(color: AppColors.primary),
// )