const amplifyConfig = r'''{
  "auth": {
    "user_pool_id": "us-east-1_IbAgT4ddo",
    "aws_region": "us-east-1",
    "user_pool_client_id": "61ng7n6fsshkiqukve97rd877b",
    "identity_pool_id": "us-east-1:58f6084a-c65b-4192-acdd-69e46f3adbe2",
    "mfa_methods": [],
    "standard_required_attributes": [
      "phone_number"
    ],
    "username_attributes": [
      "phone_number"
    ],
    "user_verification_types": [
      "phone_number"
    ],
    "groups": [],
    "mfa_configuration": "NONE",
    "password_policy": {
      "min_length": 8,
      "require_lowercase": true,
      "require_numbers": true,
      "require_symbols": true,
      "require_uppercase": true
    },
    "unauthenticated_identities_enabled": true
  },
  "data": {
    "url": "https://wczxw4cn25bt7nzvyfzrldt6ey.appsync-api.us-east-1.amazonaws.com/graphql",
    "aws_region": "us-east-1",
    "default_authorization_type": "AWS_IAM",
    "authorization_types": [
      "AMAZON_COGNITO_USER_POOLS"
    ],
    "model_introspection": {
      "version": 1,
      "models": {
        "UserInfo": {
          "name": "UserInfo",
          "fields": {
            "userId": {
              "name": "userId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "userFullName": {
              "name": "userFullName",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "userWhatsAppNumber": {
              "name": "userWhatsAppNumber",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "userStatus": {
              "name": "userStatus",
              "isArray": false,
              "type": {
                "enum": "UserInfoUserStatus"
              },
              "isRequired": false,
              "attributes": []
            },
            "createdAt": {
              "name": "createdAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            },
            "updatedAt": {
              "name": "updatedAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            }
          },
          "syncable": true,
          "pluralName": "UserInfos",
          "attributes": [
            {
              "type": "model",
              "properties": {}
            },
            {
              "type": "key",
              "properties": {
                "fields": [
                  "userId"
                ]
              }
            },
            {
              "type": "auth",
              "properties": {
                "rules": [
                  {
                    "allow": "public",
                    "provider": "iam",
                    "operations": [
                      "create",
                      "update",
                      "delete",
                      "read"
                    ]
                  }
                ]
              }
            }
          ],
          "primaryKeyInfo": {
            "isCustomPrimaryKey": true,
            "primaryKeyFieldName": "userId",
            "sortKeyFieldNames": []
          }
        },
        "UserStyleProfile": {
          "name": "UserStyleProfile",
          "fields": {
            "userId": {
              "name": "userId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "userGender": {
              "name": "userGender",
              "isArray": false,
              "type": {
                "enum": "UserStyleProfileUserGender"
              },
              "isRequired": false,
              "attributes": []
            },
            "userBodyType": {
              "name": "userBodyType",
              "isArray": false,
              "type": {
                "enum": "UserStyleProfileUserBodyType"
              },
              "isRequired": false,
              "attributes": []
            },
            "userSkinUnderTone": {
              "name": "userSkinUnderTone",
              "isArray": false,
              "type": {
                "enum": "UserStyleProfileUserSkinUnderTone"
              },
              "isRequired": false,
              "attributes": []
            },
            "userAge": {
              "name": "userAge",
              "isArray": false,
              "type": {
                "enum": "UserStyleProfileUserAge"
              },
              "isRequired": false,
              "attributes": []
            },
            "createdAt": {
              "name": "createdAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            },
            "updatedAt": {
              "name": "updatedAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            }
          },
          "syncable": true,
          "pluralName": "UserStyleProfiles",
          "attributes": [
            {
              "type": "model",
              "properties": {}
            },
            {
              "type": "key",
              "properties": {
                "fields": [
                  "userId"
                ]
              }
            },
            {
              "type": "auth",
              "properties": {
                "rules": [
                  {
                    "allow": "public",
                    "provider": "iam",
                    "operations": [
                      "create",
                      "update",
                      "delete",
                      "read"
                    ]
                  }
                ]
              }
            }
          ],
          "primaryKeyInfo": {
            "isCustomPrimaryKey": true,
            "primaryKeyFieldName": "userId",
            "sortKeyFieldNames": []
          }
        },
        "Messages": {
          "name": "Messages",
          "fields": {
            "caseId": {
              "name": "caseId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "messageId": {
              "name": "messageId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "userId": {
              "name": "userId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "messageDirection": {
              "name": "messageDirection",
              "isArray": false,
              "type": {
                "enum": "MessagesMessageDirection"
              },
              "isRequired": false,
              "attributes": []
            },
            "messageStatus": {
              "name": "messageStatus",
              "isArray": false,
              "type": {
                "enum": "MessagesMessageStatus"
              },
              "isRequired": false,
              "attributes": []
            },
            "messageType": {
              "name": "messageType",
              "isArray": false,
              "type": {
                "enum": "MessagesMessageType"
              },
              "isRequired": false,
              "attributes": []
            },
            "messageContent": {
              "name": "messageContent",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "createdAt": {
              "name": "createdAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            },
            "updatedAt": {
              "name": "updatedAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            }
          },
          "syncable": true,
          "pluralName": "Messages",
          "attributes": [
            {
              "type": "model",
              "properties": {}
            },
            {
              "type": "key",
              "properties": {
                "fields": [
                  "caseId",
                  "messageId"
                ]
              }
            },
            {
              "type": "auth",
              "properties": {
                "rules": [
                  {
                    "allow": "public",
                    "provider": "iam",
                    "operations": [
                      "create",
                      "update",
                      "delete",
                      "read"
                    ]
                  }
                ]
              }
            }
          ],
          "primaryKeyInfo": {
            "isCustomPrimaryKey": true,
            "primaryKeyFieldName": "caseId",
            "sortKeyFieldNames": [
              "messageId"
            ]
          }
        },
        "Case": {
          "name": "Case",
          "fields": {
            "userId": {
              "name": "userId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "caseId": {
              "name": "caseId",
              "isArray": false,
              "type": "String",
              "isRequired": true,
              "attributes": []
            },
            "caseStartTime": {
              "name": "caseStartTime",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": true,
              "attributes": []
            },
            "caseEndTime": {
              "name": "caseEndTime",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": []
            },
            "caseStatus": {
              "name": "caseStatus",
              "isArray": false,
              "type": {
                "enum": "CaseCaseStatus"
              },
              "isRequired": false,
              "attributes": []
            },
            "useCase": {
              "name": "useCase",
              "isArray": false,
              "type": "String",
              "isRequired": false,
              "attributes": []
            },
            "messages": {
              "name": "messages",
              "isArray": true,
              "type": "String",
              "isRequired": false,
              "attributes": [],
              "isArrayNullable": true
            },
            "createdAt": {
              "name": "createdAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            },
            "updatedAt": {
              "name": "updatedAt",
              "isArray": false,
              "type": "AWSDateTime",
              "isRequired": false,
              "attributes": [],
              "isReadOnly": true
            }
          },
          "syncable": true,
          "pluralName": "Cases",
          "attributes": [
            {
              "type": "model",
              "properties": {}
            },
            {
              "type": "key",
              "properties": {
                "fields": [
                  "userId",
                  "caseId"
                ]
              }
            },
            {
              "type": "auth",
              "properties": {
                "rules": [
                  {
                    "allow": "public",
                    "provider": "iam",
                    "operations": [
                      "create",
                      "update",
                      "delete",
                      "read"
                    ]
                  }
                ]
              }
            }
          ],
          "primaryKeyInfo": {
            "isCustomPrimaryKey": true,
            "primaryKeyFieldName": "userId",
            "sortKeyFieldNames": [
              "caseId"
            ]
          }
        }
      },
      "enums": {
        "UserInfoUserStatus": {
          "name": "UserInfoUserStatus",
          "values": [
            "NEW",
            "ONBOARDED",
            "INACTIVE"
          ]
        },
        "UserStyleProfileUserGender": {
          "name": "UserStyleProfileUserGender",
          "values": [
            "MALE",
            "FEMALE"
          ]
        },
        "UserStyleProfileUserBodyType": {
          "name": "UserStyleProfileUserBodyType",
          "values": [
            "RECTANGLE",
            "HOURGLASS",
            "ROUND",
            "INVERTED_TRIANGLE",
            "TRIANGLE"
          ]
        },
        "UserStyleProfileUserSkinUnderTone": {
          "name": "UserStyleProfileUserSkinUnderTone",
          "values": [
            "LIGHT",
            "BEIGE",
            "OLIVE",
            "TANNED",
            "DARK"
          ]
        },
        "UserStyleProfileUserAge": {
          "name": "UserStyleProfileUserAge",
          "values": [
            "AGE_18_24",
            "AGE_25_34",
            "AGE_35_44",
            "AGE_45_54",
            "AGE_55_64"
          ]
        },
        "MessagesMessageDirection": {
          "name": "MessagesMessageDirection",
          "values": [
            "INBOUND",
            "OUTBOUND"
          ]
        },
        "MessagesMessageStatus": {
          "name": "MessagesMessageStatus",
          "values": [
            "SENT",
            "DELIVERED",
            "READ",
            "RECEIVED"
          ]
        },
        "MessagesMessageType": {
          "name": "MessagesMessageType",
          "values": [
            "TEXT",
            "IMAGE",
            "INTERACTIVE"
          ]
        },
        "CaseCaseStatus": {
          "name": "CaseCaseStatus",
          "values": [
            "NEW",
            "USECASE_IDENTIFIED",
            "CONTEXT_GATHERED",
            "OUTPUT_SENT",
            "FEEDBACK_LOOP",
            "CLOSED"
          ]
        }
      },
      "nonModels": {}
    }
  },
  "version": "1.3",
  "custom": {
    "API": {
      "chatApi": {
        "endpoint": "https://ti4uv2kkze.execute-api.us-east-1.amazonaws.com/dev/",
        "region": "us-east-1",
        "apiName": "chatApi"
      },
      "userApi": {
        "endpoint": "https://8ihacjaar2.execute-api.us-east-1.amazonaws.com/dev/",
        "region": "us-east-1",
        "apiName": "userApi"
      },
      "gatewayApi": {
        "endpoint": "https://0slkvlq7pa.execute-api.us-east-1.amazonaws.com/dev/",
        "region": "us-east-1",
        "apiName": "gatewayApi"
      },
      "caseApi": {
        "endpoint": "https://jai0vltavf.execute-api.us-east-1.amazonaws.com/dev/",
        "region": "us-east-1",
        "apiName": "caseApi"
      },
      "maisApi": {
        "endpoint": "https://q8x8i7m0rj.execute-api.us-east-1.amazonaws.com/dev/",
        "region": "us-east-1",
        "apiName": "maisApi"
      }
    }
  }
}''';