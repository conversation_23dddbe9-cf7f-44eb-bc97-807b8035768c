import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/authScreens/signIn/loginScreen.dart';
import 'package:monova_ai_stylist/pages/sideNavBarPages/privacyPolicyPage.dart';
import 'package:monova_ai_stylist/pages/sideNavBarPages/settingsPage.dart';
import 'package:monova_ai_stylist/pages/onboardingForms/WelcomeScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/archive/archiveHomeScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/favourites/favouritesHomeScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/styleGuide/styleGuide.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/stylePrefrences/stylePreferencesScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/aboutMe.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/services/weatherService.dart';
import 'package:shimmer/shimmer.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _userName = '';
  bool _isLoading = true;
  String _profileImageUrl =
      'assets/staticImages/profile.png'; // Default image path
  String weatherLocation = '--';
  bool isWeatherLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchUserData();
    _fetchProfileImage();
    _loadWeatherLocation();
  }

  Future<void> _loadWeatherLocation() async {
    setState(() {
      isWeatherLoading = true;
    });

    try {
      final weatherData = await WeatherLocationService.getWeatherData();

      if (mounted) {
        setState(() {
          weatherLocation = weatherData.location;
          isWeatherLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          weatherLocation = 'NA'; // Fallback
          isWeatherLoading = false;
        });
      }
    }
  }

  Future<void> _fetchProfileImage() async {
    try {
      final userId = await SharedPrefsService.getUserId();
      final token = await SharedPrefsService.getAuthToken();

      if (userId == null || token == null) {
        return;
      }

      var headers = {'Authorization': 'Bearer $token'};

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.styleProfileApi}style-profile?userId=$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        final responseData = await response.stream.bytesToString();
        final jsonResponse = jsonDecode(responseData);

        if (jsonResponse['data'] != null &&
            jsonResponse['data']['userProfileImageUrl'] != null) {
          setState(() {
            _profileImageUrl = jsonResponse['data']['userProfileImageUrl'];
          });
        }
      }
    } catch (e) {
      print('Error fetching profile image: $e');
    }
  }

  Future<void> _fetchUserData() async {
    try {
      final userId = await SharedPrefsService.getUserId();

      if (userId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.userApi}user'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({"operation": "get", "userId": userId}),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        setState(() {
          _userName = jsonResponse['data']['userFullName'];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 16),
              const Center(
                child: Text(
                  'Account',
                  style: TextStyle(
                    fontFamily: "SatoshiM",
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // Profile Picture
              Center(
                child: InkWell(
                  // onTap: () {
                  //   Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //       builder: (context) => ProfilePhotoScreen(
                  //         imageUrl: _profileImageUrl,
                  //       ),
                  //     ),
                  //   );
                  // },
                  child: _profileImageUrl.startsWith('http')
                      ? CircleAvatar(
                          radius: 70,
                          backgroundImage: NetworkImage(_profileImageUrl),
                        )
                      : CircleAvatar(
                          radius: 70,
                          backgroundImage: AssetImage(_profileImageUrl),
                        ),
                ),
              ),
              const SizedBox(height: 16),
              // Name
              Center(
                child: _isLoading
                    ? const CircularProgressIndicator(
                        color: Color(0xFFE05D38),
                      )
                    : Text(
                        _userName,
                        style: const TextStyle(
                          fontFamily: "SatoshiB",
                          fontSize: 22,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
              const SizedBox(height: 8),
              // Location
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      // Navigator.push(
                      //   context,
                      //   MaterialPageRoute(
                      //     builder: (context) => const LocationScreen(),
                      //   ),
                      // ).then((_) {
                      //   // Refresh location when returning from location screen
                      //   _loadWeatherLocation();
                      // });
                    },
                    child: Row(
                      children: [
                        isWeatherLoading
                            ? Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  height: 14,
                                  width: 100,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              )
                            : Text(
                                weatherLocation,
                                style: const TextStyle(
                                  fontFamily: "SatoshiR",
                                  fontSize: 14,
                                  color: Colors.black87,
                                ),
                              ),
                        // const SizedBox(width: 8),
                        // Icon(FeatherIcons.edit2,
                        //     size: 12, color: Colors.grey.shade600),
                      ],
                    ),
                  ),
                ],
              ),
              // const SizedBox(height: 24),
              // Stats Row
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   children: [
              //     _buildStatItem('15', 'Items'),
              //     Container(
              //       height: 24,
              //       width: 1,
              //       color: Colors.grey.shade300,
              //       margin: const EdgeInsets.symmetric(horizontal: 16),
              //     ),
              //     _buildStatItem('62', 'Outfits'),
              //   ],
              // ),
              const SizedBox(height: 16),

              // Grey background section for all menu items
              Container(
                color: const Color(
                    0xFFF9F9F9), // Light grey background for entire menu
                child: Column(
                  children: [
                    // Menu Items (first seoction)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        children: [
                          SizedBox(height: 14),
                          _buildMenuItem(context, FeatherIcons.star, 'About Me',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const AboutMeScreen()),
                            );
                          }),
                          _buildMenuItem(
                              context, FeatherIcons.bookmark, 'Saved',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const FavouriteScreen()),
                            );
                          }),
                          _buildMenuItem(context, FeatherIcons.shoppingBag,
                              'Style preferences', onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const StylePreferencesScreen()),
                            );
                          }),
                          _buildMenuItem(
                              context, FeatherIcons.folder, 'Style guide',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const StyleGuideScreen()),
                            );
                          }),
                          _buildMenuItem(
                              context, FeatherIcons.archive, 'Archive',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const ArchivedScreen()),
                            );
                          }),
                          SizedBox(height: 14),
                        ],
                      ),
                    ),

                    // Divider line between sections
                    const Divider(
                        height: 1, thickness: 1, color: Color(0xFFEEEEEE)),

                    // Second section of menu items
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 14),
                          _buildMenuItem(
                              context, FeatherIcons.lock, 'Privacy & Security',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const PrivacyPolicyScreen()),
                            );
                          }),
                          _buildMenuItem(context, FeatherIcons.bell,
                              'Notification & Reminders', onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const SettingsPage()),
                            );
                          }),
                          _buildMenuItem(
                              context, FeatherIcons.send, 'Invite a Friend',
                              onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const Welcomescreen()),
                            );
                          }),
                          SizedBox(height: 14),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Logout section with top divider
              Container(
                color: const Color(0xFFF9F9F9), // Match the background color
                child: Column(
                  children: [
                    const Divider(
                        height: 1, thickness: 1, color: Color(0xFFEEEEEE)),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 32.0, bottom: 32.0, left: 24.0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: InkWell(
                          onTap: () {
                            // Show confirmation dialog
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  backgroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  title: const Text(
                                    'Logout',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontFamily: "SatoshiB",
                                      color: Color(0xFF1F1F1F),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  content: const Text(
                                    'Do you want to logout?',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontFamily: "SatoshiR",
                                      color: Color(0xFF1F1F1F),
                                    ),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(),
                                      child: const Text(
                                        'Cancel',
                                        style: TextStyle(
                                          color: Color(0xFF646468),
                                          fontSize: 16,
                                          fontFamily: "SatoshiR",
                                        ),
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () async {
                                        try {
                                          // Clear all Hive boxes related to caching
                                          final boxNames = [
                                            'daily_outfits',
                                            'weekly_outfits',
                                            'explore_items_cache',
                                            'outfits_screen_cache',
                                          ];

                                          // Close and clear each box
                                          for (var boxName in boxNames) {
                                            if (Hive.isBoxOpen(boxName)) {
                                              final box = Hive.box(boxName);
                                              await box
                                                  .clear(); // Clear all data
                                              await box
                                                  .close(); // Close the box
                                              print(
                                                  'Cleared and closed Hive box: $boxName');
                                            }
                                          }

                                          // Clear shared preferences auth data
                                          await SharedPrefsService
                                              .clearAuthData();

                                          if (context.mounted) {
                                            Navigator.of(context)
                                                .pushAndRemoveUntil(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const SignInLoginScreen(),
                                              ),
                                              (route) => false,
                                            );
                                          }
                                        } catch (e) {
                                          print(
                                              'Error clearing Hive data on logout: $e');
                                          // Still attempt to logout even if clearing cache fails
                                          await SharedPrefsService
                                              .clearAuthData();
                                          if (context.mounted) {
                                            Navigator.of(context)
                                                .pushAndRemoveUntil(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const SignInLoginScreen(),
                                              ),
                                              (route) => false,
                                            );
                                          }
                                        }
                                      },
                                      child: const Text(
                                        'Yes',
                                        style: TextStyle(
                                          color: Color(0xFFE05D38),
                                          fontSize: 16,
                                          fontFamily: "SatoshiB",
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: const Text(
                            'LOG OUT',
                            style: TextStyle(
                              color: Color(0xFFE05D38),
                              fontFamily: "SatoshiM",
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 1.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            fontFamily: "SatoshiB",
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(BuildContext context, IconData iconData, String title,
      {required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Row(
          children: [
            Icon(iconData, size: 22, color: Colors.black87),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontFamily: "SatoshiM",
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Icon(
              Icons.chevron_right,
              size: 18,
              color: Colors.black54,
            ),
          ],
        ),
      ),
    );
  }
}
