import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/pages/features/wadrobe/search/collectionDetailScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/favourites/favouriteItems/favouriteItemDetailsScreen.dart';
import 'dart:convert';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class DetailedApparelItem {
  final String apparelId;
  final String apparelProfile;
  final String apparelType;
  final String colour;
  final String brand;
  final String fit;
  final String pattern;
  final String apparelMediaUrl;
  final String productId;
  final String productName;
  final String wardrobeApparelStatus;
  final String fabric;
  final String length;
  final String transparency;
  final String waistRise;
  final String weave;
  final String occasion;
  final List<String> seasons;

  DetailedApparelItem({
    required this.apparelId,
    required this.apparelProfile,
    required this.apparelType,
    required this.colour,
    required this.brand,
    required this.fit,
    required this.pattern,
    required this.apparelMediaUrl,
    required this.productId,
    required this.productName,
    this.wardrobeApparelStatus = '',
    this.fabric = '',
    this.length = '',
    this.transparency = '',
    this.waistRise = '',
    this.weave = '',
    this.occasion = '',
    this.seasons = const [],
  });

  factory DetailedApparelItem.fromJson(Map<String, dynamic> json) {
    return DetailedApparelItem(
      apparelId: json['apparelId'],
      apparelProfile: json['apparelProfile'],
      apparelType: json['apparelType'],
      colour: json['colour'],
      brand: json['brand'],
      fit: json['fit'],
      pattern: json['pattern'],
      apparelMediaUrl: json['apparelMediaUrl'],
      productId: json['productId'],
      productName: json['productName'],
      wardrobeApparelStatus: json['wardrobeApparelStatus'],
      fabric: json['fabric'],
      length: json['length'],
      transparency: json['transparency'],
      waistRise: json['waistRise'],
      weave: json['weave'],
      occasion: json['occasion'],
      seasons: json['seasons'] != null
          ? (json['seasons'] is List
              ? List<String>.from(json['seasons'])
              : [json['seasons'].toString()])
          : [],
    );
  }

  // Check if the item is favourited
  bool isFavourite() {
    return wardrobeApparelStatus.toUpperCase() == 'FAVOURITES';
  }

  // Convert to a Map for FavouriteItemDetailsScreen
  Map<String, dynamic> toMap() {
    return {
      'apparelId': apparelId,
      'image': apparelMediaUrl,
      'name': productName.isNotEmpty ? productName : apparelType,
      'brand': brand,
      'colour': colour,
      'pattern': pattern,
      'fit': fit,
      'fabric': fabric,
      'length': length,
      'transparency': transparency,
      'waistRise': waistRise,
      'weave': weave,
      'occasion': occasion,
      'seasons': seasons.isEmpty ? 'NA' : seasons.join(', '),
      'apparelType': apparelType,
      'date':
          'Added on ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
      'productId': productId,
      'apparelProfile': apparelProfile,
    };
  }
}

class FavouriteScreen extends StatefulWidget {
  const FavouriteScreen({Key? key}) : super(key: key);

  @override
  State<FavouriteScreen> createState() => _FavouriteScreenState();
}

class _FavouriteScreenState extends State<FavouriteScreen> {
  String _selectedView = 'Saved Items';
  bool _isItemsView = true;
  String _selectedCategory = 'All';
  bool _isLoading = false;
  List<DetailedApparelItem> _favouriteItems = [];
  String? _authToken;
  String? _userId;
  List<OutfitCollection> _collections = [];

  final List<String> _viewOptions = ['Saved Items', 'Saved Outfits'];

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _fetchCollections(); // Add this line
  }

  Future<void> _fetchCollections() async {
    setState(() => _isLoading = true);

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() => _isLoading = false);
        return;
      }

      final uri = Uri.parse(
          '${ApiConstants.outfitApi}outfit/collections/list?userId=$userId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _collections = (data['data'] as List)
              .map((item) => OutfitCollection.fromJson(item))
              .toList();
        });
      } else {
        throw Exception('Failed to load collections');
      }
    } catch (e) {
      print('Error fetching collections: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadUserData() async {
    try {
      _authToken = await SharedPrefsService.getAuthToken();
      _userId = await SharedPrefsService.getUserId();

      if (_authToken != null && _userId != null) {
        await _fetchFavouriteItems();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Authentication error. Please login again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _fetchFavouriteItems() async {
    if (_authToken == null || _userId == null) return;

    setState(() => _isLoading = true);

    try {
      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/list?source=USER&sourceId=$_userId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Use DetailedApparelItem to parse the response
        final List<DetailedApparelItem> allItems = (data['data'] as List)
            .map((item) => DetailedApparelItem.fromJson(item))
            .toList();

        // Filter to include only favourite items
        final List<DetailedApparelItem> favourites =
            allItems.where((item) => item.isFavourite()).toList();

        setState(() {
          _favouriteItems = favourites;
        });
      } else {
        throw Exception(
            'Failed to load favourite items: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching favourite items: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading favourite items: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildDropdownHeader() {
    return PopupMenuButton<String>(
      initialValue: _selectedView,
      onSelected: (String value) {
        setState(() {
          _selectedView = value;
          _isItemsView = value == 'Saved Items';
        });
      },
      offset: const Offset(0, 40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: Color(0xFFE7E7E7), width: 1),
      ),
      color: Colors.white,
      enableFeedback: false,
      // These properties prevent highlighting
      splashRadius: 0,
      tooltip: '',
      elevation: 4,
      padding: EdgeInsets.zero,
      // Custom popup menu item builder to avoid all highlighting
      itemBuilder: (BuildContext context) => _viewOptions.map((String value) {
        return PopupMenuItem<String>(
          value: value,
          enabled: true,
          padding: EdgeInsets.zero,
          height: 40,
          textStyle: TextStyle(color: Colors.transparent),
          // Disable all highlighting effects
          child: Container(
            width: double.infinity,
            height: 40,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: value != _viewOptions.last
                    ? BorderSide(color: Colors.grey.shade200, width: 1)
                    : BorderSide.none,
              ),
            ),
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: "SatoshiM",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        );
      }).toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xffF8F7F7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _selectedView,
              style: const TextStyle(
                fontFamily: "SatoshiM",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 6),
            const Icon(Icons.keyboard_arrow_down,
                color: Colors.black, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryPill(String text, int count, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategory = text;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFF3F3) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Text(
              text,
              style: TextStyle(
                fontFamily: "SatoshiM",
                color: isSelected ? Colors.black : Colors.grey,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFFF4646) : Colors.grey[400],
                shape: BoxShape.circle,
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  fontFamily: "SatoshiM",
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavouriteItemsView() {
    // Calculate category counts
    final categoryCounts = {
      'All': _favouriteItems.length,
      'Tops': _favouriteItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('top') ||
              item.apparelType.toLowerCase().contains('shirt') ||
              item.apparelType.toLowerCase().contains('tshirt') ||
              item.apparelType.toLowerCase().contains('t-shirt') ||
              item.apparelType.toLowerCase().contains('blouse'))
          .length,
      'Bottoms': _favouriteItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('pant') ||
              item.apparelType.toLowerCase().contains('trouser') ||
              item.apparelType.toLowerCase().contains('short') ||
              item.apparelType.toLowerCase().contains('skirt') ||
              item.apparelType.toLowerCase().contains('bottom'))
          .length,
      'Shoes': _favouriteItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('shoe') ||
              item.apparelType.toLowerCase().contains('boot') ||
              item.apparelType.toLowerCase().contains('sneaker') ||
              item.apparelType.toLowerCase().contains('sandal'))
          .length,
    };

    return Column(
      children: [
        // Category filters
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCategoryPill('All', categoryCounts['All']!,
                          _selectedCategory == 'All'),
                      _buildCategoryPill('Tops', categoryCounts['Tops']!,
                          _selectedCategory == 'Tops'),
                      _buildCategoryPill('Bottoms', categoryCounts['Bottoms']!,
                          _selectedCategory == 'Bottoms'),
                      _buildCategoryPill('Shoes', categoryCounts['Shoes']!,
                          _selectedCategory == 'Shoes'),
                    ],
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.tune, color: Colors.black54),
                    onPressed: () => showFilterModalSheet(context),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Items grid
        Expanded(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: Color(0xFFF76037)))
              : _favouriteItems.isEmpty
                  ? Column(
                      children: [
                        Image.asset(
                          'assets/emptyImages/tshirt_no.png',
                          width: 480,
                          height: 480,
                        ),
                        const Center(
                            child: Text('You have no saved items!',
                                style: TextStyle(
                                    fontFamily: "SatoshiR", fontSize: 16))),
                      ],
                    )
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio:
                            0.75, // Changed from 0.82 to 0.75 to match AddItemPage
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: _favouriteItems.length,
                      itemBuilder: (context, index) {
                        final item = _favouriteItems[index];

                        // Filter by selected category
                        if (_selectedCategory != 'All') {
                          if (_selectedCategory == 'Tops' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('top') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('shirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('tshirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('t-shirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('blouse'))) {
                            return const SizedBox.shrink();
                          }

                          if (_selectedCategory == 'Bottoms' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('pant') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('trouser') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('short') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('skirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('bottom'))) {
                            return const SizedBox.shrink();
                          }

                          if (_selectedCategory == 'Shoes' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('shoe') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('boot') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('sneaker') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('sandal'))) {
                            return const SizedBox.shrink();
                          }
                        }

                        return _buildFavouriteItemCard(item);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildFavouriteItemCard(DetailedApparelItem item) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FavouriteItemDetailsScreen(
              item: item.toMap(),
              onRemoveFavourite: _removeFavourite,
            ),
          ),
        ).then((_) => _fetchFavouriteItems());
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image container
          Container(
            width: double.infinity,
            height: 160,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.transparent,
                width: 0,
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Image
                item.apparelMediaUrl.isNotEmpty
                    ? Image.network(
                        item.apparelMediaUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.image_not_supported,
                              color: Colors.grey),
                        ),
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported,
                            color: Colors.grey),
                      ),
              ],
            ),
          ),

          // Text information
          const SizedBox(height: 8),

          // Product name
          Text(
            item.productName.isNotEmpty ? item.productName : item.apparelType,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: "SatoshiR",
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 2),

          // Brand and color
          if (item.brand.isNotEmpty || item.colour.isNotEmpty)
            Text(
              [
                if (item.brand.isNotEmpty) item.brand,
                if (item.colour.isNotEmpty) item.colour
              ].join(' • '),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 12,
                fontFamily: "SatoshiR",
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Future<bool> _removeFavourite(Map<String, dynamic> item) async {
    if (_authToken == null || _userId == null) return false;

    try {
      final apparelId = item['apparelId'];
      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for removal');
      }

      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/$apparelId?sourceId=$_userId');

      final response = await http.patch(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({"wardrobeApparelStatus": "PRIMARY"}),
      );

      if (response.statusCode == 200) {
        print('Item removed from favourites successfully');
        return true;
      } else {
        print(
            'Failed to remove item from favourites: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error removing item from favourites: $e');
      return false;
    }
  }

  Widget _buildFavouriteOutfitsView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _collections.isEmpty
              ? const Center(
                  child: Text(
                    'No style journals yet',
                    style: TextStyle(
                      fontFamily: "SatoshiR",
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                )
              : GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1,
                  ),
                  itemCount: _collections.length,
                  itemBuilder: (context, index) {
                    final collection = _collections[index];
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CollectionDetailsScreen(
                              collection: collection,
                            ),
                          ),
                        );
                      },
                      child: _buildStyleJournalCard(collection),
                    );
                  },
                ),
    );
  }

  List<Color> _getGradientColorsForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return [const Color(0xFFFFF8E7), const Color(0xFFFFE5C4)];
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return [const Color(0xFFFFF1F1), const Color(0xFFFFD6D6)];
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return [const Color(0xFFF5F1FF), const Color(0xFFE8E0FF)];
    }
    // Default gradient
    return [const Color(0xFFE3F2FD), const Color(0xFFBBDEFB)];
  }

  IconData _getIconForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return Icons.work_outline;
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return Icons.favorite;
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return Icons.wb_sunny_outlined;
    }
    return Icons.style;
  }

  Widget _buildStyleJournalCard(OutfitCollection collection) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.28,
      height: MediaQuery.of(context).size.width * 0.28,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              _getGradientColorsForCollection(collection.outfitCollectionName),
        ),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Stack(
        children: [
          // Existing code remains the same, using collection properties
          Positioned(
            top: 12,
            right: 12,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: collection.outfitCollectionMediaUrl.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: Image.network(
                          collection.outfitCollectionMediaUrl,
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            _getIconForCollection(
                                collection.outfitCollectionName),
                            size: 24,
                            color: Colors.black,
                          ),
                        ),
                      )
                    : Icon(
                        _getIconForCollection(collection.outfitCollectionName),
                        size: 24,
                        color: Colors.black,
                      ),
              ),
            ),
          ),
          Positioned(
            bottom: 16,
            left: 16,
            child: Text(
              collection.outfitCollectionName,
              style: const TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 18,
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showFilterModalSheet(BuildContext context) {
    // Implement filter modal sheet
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF8F7F7),
      appBar: AppBar(
        toolbarHeight: 80,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: _buildDropdownHeader(),
        centerTitle: true,
      ),
      body: _isItemsView
          ? _buildFavouriteItemsView()
          : _buildFavouriteOutfitsView(),
    );
  }
}
