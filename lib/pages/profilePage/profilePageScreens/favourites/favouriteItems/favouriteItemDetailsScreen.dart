import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

class FavouriteItemDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> item;
  final Function(Map<String, dynamic>) onRemoveFavourite;

  const FavouriteItemDetailsScreen({
    Key? key,
    required this.item,
    required this.onRemoveFavourite,
  }) : super(key: key);

  @override
  State<FavouriteItemDetailsScreen> createState() =>
      _FavouriteItemDetailsScreenState();
}

class _FavouriteItemDetailsScreenState
    extends State<FavouriteItemDetailsScreen> {
  bool isExpanded = false;
  bool _isRemoving = false;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    final availableHeight = size.height - padding.top - padding.bottom;
    final itemDetails = getItemDetails();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.item['name'],
          style: const TextStyle(
            fontFamily: "SatoshiR",
            color: Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return ListView(
            padding: EdgeInsets.all(size.width * 0.04),
            children: [
              // Image Container with Score Section
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFEFEFEF), width: 1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Image container
                    Container(
                      height: size.height * 0.45,
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: widget.item['image'] != null &&
                                widget.item['image']
                                    .toString()
                                    .startsWith('http')
                            ? Image.network(
                                widget.item['image'],
                                width: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Container(
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.image_not_supported,
                                      color: Colors.grey),
                                ),
                              )
                            : Image.asset(
                                widget.item['image'] ??
                                    'assets/staticImages/cloth.png',
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    _buildScoreSection(size),
                  ],
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Details List
              _buildDetailItem('Name', itemDetails['name']),
              _buildDetailItem('Seasons', itemDetails['seasons'],
                  showArrow: false),
              _buildColorDetailItem(itemDetails['color']),
              _buildPatternDetailItem(itemDetails['pattern']),
              SizedBox(height: size.height * 0.02),

              // Additional Details Section
              _buildAdditionalDetails(size),
              if (isExpanded) ...[
                SizedBox(height: size.height * 0.02),
                _buildDetailItem('Category', itemDetails['category'],
                    showArrow: false),
                _buildDetailItem('Occasion', itemDetails['occasions'],
                    showArrow: false),
                _buildDetailItem('Mood', itemDetails['mood'], showArrow: false),
              ],

              // Remove from Favourites Button
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: size.width * 0.04,
                    vertical: size.height * 0.02),
                child: ElevatedButton(
                  onPressed: _isRemoving ? null : _removeFavourite,
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(48),
                      side: const BorderSide(color: Color(0xFFE7E7E7)),
                    ),
                    backgroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: _isRemoving
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Color(0xFFF76037),
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Remove from Favourites',
                          style: TextStyle(
                            color: Color(0xFFF76037),
                            fontFamily: "SatoshiM",
                            fontSize: 16,
                          ),
                        ),
                ),
              ),

              SizedBox(height: availableHeight * 0.05),
            ],
          );
        },
      ),
    );
  }

  Future<void> _removeFavourite() async {
    setState(() {
      _isRemoving = true;
    });

    try {
      final success = await widget.onRemoveFavourite(widget.item);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Item removed from favourites'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context); // Return to favourites screen
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to remove item from favourites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error in remove favourite button handler: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRemoving = false;
        });
      }
    }
  }

  Map<String, dynamic> getItemDetails() {
    // Extract or provide default values for item details
    final details = <String, dynamic>{
      'name': widget.item['name'],
      'seasons': widget.item['seasons'],
      'color': widget.item['colour'],
      'pattern': widget.item['pattern'],
      'category': widget.item['apparelType'],
      'occasions': widget.item['occasion'],
      'mood': widget.item['mood'],
    };

    return details;
  }

  Widget _buildScoreSection(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
          child: Text(
            'Apparel Score',
            style: TextStyle(
              fontFamily: "SatoshiR",
              color: Colors.grey,
              fontSize: size.width * 0.045,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: size.width * 0.04,
            vertical: size.height * 0.01,
          ),
          height: size.height * 0.025,
          decoration: BoxDecoration(
            color: const Color(0xFFE7E7E7),
            borderRadius: BorderRadius.circular(14),
          ),
          child: Stack(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 25,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFE7E7E7),
                        borderRadius: BorderRadius.circular(14),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 25,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFE7E7E7),
                        borderRadius: BorderRadius.circular(14),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 25,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFE7E7E7),
                        borderRadius: BorderRadius.circular(14),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 19,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFE7E7E7),
                        borderRadius: BorderRadius.circular(14),
                      ),
                    ),
                  ),
                ],
              ),
              Positioned.fill(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildScoreText('25%', size),
                    _buildScoreText('25%', size),
                    _buildScoreText('25%', size),
                    _buildScoreText('19%', size),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.04,
            vertical: size.height * 0.005,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildScoreLabel('Colour', size),
              _buildScoreLabel('Fitting', size),
              _buildScoreLabel('Fabric', size),
              _buildScoreLabel('Style', size),
            ],
          ),
        ),
        SizedBox(height: size.height * 0.01),
      ],
    );
  }

  Widget _buildScoreText(String text, Size size) {
    return Text(
      text,
      style: TextStyle(
        fontSize: size.width * 0.025,
        fontFamily: "SatoshiR",
        color: Colors.black87,
      ),
    );
  }

  Widget _buildScoreLabel(String text, Size size) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: "SatoshiR",
        color: Colors.grey,
        fontSize: size.width * 0.03,
      ),
    );
  }

  Widget _buildDetailItem(String title, String value,
      {bool showArrow = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(
                  child: Text(
                    value,
                    textAlign: TextAlign.right,
                    style:
                        const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                  ),
                ),
                if (showArrow)
                  const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorDetailItem(String color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Colour',
            style: TextStyle(fontFamily: "SatoshiR", fontSize: 14),
          ),
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFB5D1C9),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Text(
                color,
                style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
              ),
              // const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPatternDetailItem(String pattern) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Pattern',
            style: TextStyle(fontFamily: "SatoshiR", fontSize: 14),
          ),
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F1F1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Text(
                pattern,
                style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
              ),
              // const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalDetails(Size size) {
    return InkWell(
      onTap: () => setState(() => isExpanded = !isExpanded),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Additional Details',
            style: TextStyle(
              fontFamily: "SatoshiR",
              fontSize: size.width * 0.035,
              fontWeight: FontWeight.w500,
            ),
          ),
          Icon(
            isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: Colors.black87,
            size: size.width * 0.06,
          ),
        ],
      ),
    );
  }
}
