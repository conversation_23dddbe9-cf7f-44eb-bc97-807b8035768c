import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditAgepage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditBodyColorPage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditBodyTypePage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditGenderPage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditHeightPage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/aboutMe/EditNamePage.dart';
import 'package:monova_ai_stylist/services/sharedPrefsService.dart';

class AboutMeScreen extends StatefulWidget {
  const AboutMeScreen({Key? key}) : super(key: key);

  @override
  State<AboutMeScreen> createState() => _AboutMeScreenState();
}

class _AboutMeScreenState extends State<AboutMeScreen> {
  Map<String, dynamic> _userData = {};
  bool _isLoading = true;
  String? _error;
  String _userName = 'NA';
  String? _profilePhotoUrl; // Add this line

  @override
  void initState() {
    super.initState();
    _fetchUserStyleProfile();
    _fetchUserName();
    _getUserDataFromLocalAndRemote();
  }

  Future<void> _getUserDataFromLocalAndRemote() async {
    // First try to get data from local storage
    final localName = await SharedPrefsService.getFullName();
    final localPhoto = await SharedPrefsService.getProfilePhoto();

    if (localName != null) {
      setState(() {
        _userName = localName;
      });
    }

    if (localPhoto != null) {
      setState(() {
        _profilePhotoUrl = localPhoto;
      });
    }

    // Then fetch updated data from server
    await _fetchUserName();
  }

  Future<void> _fetchUserName() async {
    try {
      final userId = await SharedPrefsService.getUserId();

      if (userId == null) {
        return;
      }

      var headers = {'Content-Type': 'application/json'};

      var request =
          http.Request('POST', Uri.parse('${ApiConstants.userApi}user'));
      request.body = json.encode({"operation": "get", "userId": userId});
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        final responseData = await response.stream.bytesToString();
        final jsonResponse = jsonDecode(responseData);

        if (jsonResponse['data'] != null) {
          if (jsonResponse['data']['userFullName'] != null) {
            final newName = jsonResponse['data']['userFullName'];
            // Save to shared preferences
            await SharedPrefsService.saveFullName(newName);

            setState(() {
              _userName = newName;
              // Update the name in the _userData map as well
              if (_userData.isNotEmpty) {
                _userData['userFullName'] = _userName;
              }
            });
          }

          // Handle profile photo if available
          if (jsonResponse['data']['profilePhotoUrl'] != null) {
            final photoUrl = jsonResponse['data']['profilePhotoUrl'];
            // Save to shared preferences
            await SharedPrefsService.saveProfilePhoto(photoUrl);

            setState(() {
              _profilePhotoUrl = photoUrl;
            });
          }
        }
      }
    } catch (e) {
      print('Error fetching user data: $e');
    }
  }

  Future<void> _fetchUserStyleProfile() async {
    try {
      // Get user ID and auth token from shared preferences
      final userId = await SharedPrefsService.getUserId();
      final authToken = await SharedPrefsService.getAuthToken();

      if (userId == null || authToken == null) {
        setState(() {
          _error = 'Authentication failed';
          _isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        setState(() {
          _userData = jsonResponse['data'];
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Failed to load user data';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return LoaderOverlay(
        isLoading: _isLoading,
        gifAsset: 'assets/loader/loading.gif',
        child: Scaffold(
          backgroundColor: const Color(0xFFF8F7F7),
          appBar: AppBar(
            backgroundColor: const Color(0xFFF8F7F7),
            elevation: 0,
            centerTitle: true,
            title: const Text(
              'About me',
              style: TextStyle(
                color: Color(0xFF1F1F1F),
                fontSize: 18,
                fontFamily: "SatoshiM",
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: const Center(
              // child: CircularProgressIndicator(
              //   color: Color(0xFFE05D38),
              // ),
              ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        backgroundColor: const Color(0xFFF8F7F7),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF8F7F7),
          elevation: 0,
          centerTitle: true,
          title: const Text(
            'About me',
            style: TextStyle(
              color: Color(0xFF1F1F1F),
              fontSize: 18,
              fontFamily: "SatoshiM",
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Center(
          child: Text(
            _error!,
            style: const TextStyle(color: Colors.red),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F7F7),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF8F7F7),
        elevation: 0,
        centerTitle: true,
        title: const Text(
          'About me',
          style: TextStyle(
            color: Color(0xFF1F1F1F),
            fontSize: 18,
            fontFamily: "SatoshiM",
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1F1F1F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              // Personal Details Header
              const Text(
                'Personal Details',
                style: TextStyle(
                  color: Color(0xFF1F1F1F),
                  fontSize: 16,
                  fontFamily: "SatoshiM",
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),

              // Name
              // Name
              Container(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Name',
                      style: TextStyle(
                        color: Color(0xFF1F1F1F),
                        fontSize: 14,
                        fontFamily: "SatoshiM",
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditNamePage(
                              initialName: _userName != 'NA'
                                  ? _userName
                                  : (_userData['userFullName']),
                              onNameUpdated: (newName) {
                                setState(() {
                                  _userName = newName;
                                  if (_userData.isNotEmpty) {
                                    _userData['userFullName'] = newName;
                                  }
                                });
                              },
                            ),
                          ),
                        );
                        // Refresh data after returning
                        _fetchUserStyleProfile();
                      },
                      child: Row(
                        children: [
                          Text(
                            _userName != 'NA'
                                ? _userName
                                : (_userData['userFullName']),
                            style: const TextStyle(
                              color: Color(0xFF1F1F1F),
                              fontSize: 14,
                              fontFamily: "SatoshiR",
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.chevron_right,
                            size: 20,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(color: Color(0xFFE7E7E7), height: 16),

              // Age
              buildInfoRow(
                label: 'Age',
                value: _mapAgeToReadable(_userData['userAge']),
                canEdit: true,
                onTap: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditAgePage(
                        initialAge:
                            _mapAgeToReadable(_userData['userAge']),
                        onAgeUpdated: (newAge) {
                          // Update age in your state or database
                        },
                      ),
                    ),
                  );
                  // Refresh data after returning
                  _fetchUserStyleProfile();
                },
              ),

              // Style Identity
              buildInfoRow(
                label: 'Style Identity',
                value: _mapGenderToStyleIdentity(_userData['userGender']),
                canEdit: true,
                onTap: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditStyleIdentityPage(
                        initialStyle: _mapGenderToStyleIdentity(
                            _userData['userGender']),
                        onStyleUpdated: (newStyle) {
                          // Update style in your state or database
                        },
                      ),
                    ),
                  );
                  // Refresh data after returning
                  _fetchUserStyleProfile();
                },
              ),

              const SizedBox(height: 24),

              // Physical Details Header
              const Text(
                'Physical Details',
                style: TextStyle(
                  color: Color(0xFF1F1F1F),
                  fontSize: 16,
                  fontFamily: "SatoshiM",
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),

              // Height
              buildInfoRow(
                label: 'Height',
                value: _userData['userHeightMetric'] ?? 'Not specified',
                canEdit: true,
                onTap: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditHeightPage(
                          initialHeight: _userData['userHeightMetric'],
                          onHeightUpdated: (newHeight) {
                            // Update height in your state or database
                          }),
                    ),
                  );
                  // Refresh data after returning
                  _fetchUserStyleProfile();
                },
              ),

              // Body type
              buildInfoRow(
                label: 'Body type',
                value: _mapBodyType(_userData['userBodyType']),
                canEdit: true,
                onTap: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditBodyTypePage(
                        initialBodyType:
                            _mapBodyType(_userData['userBodyType']),
                        onBodyTypeUpdated: (newBodyType) {
                          // Update body type in your state or database
                        },
                      ),
                    ),
                  );
                  // Refresh data after returning
                  _fetchUserStyleProfile();
                },
              ),

              // Undertone
              buildUndertoneRow(
                label: 'Undertone',
                value: _mapUndertone(_userData['userUndertone']),
                canEdit: true,
                onTap: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditBodyColorPage(
                          initialBodyColor:
                              _mapUndertone(_userData['userUndertone']),
                          onBodyColorUpdated: (newColor) {
                            // Update body color in your state or database
                          }),
                    ),
                  );
                  // Refresh data after returning
                  _fetchUserStyleProfile();
                },
              ),

              // Complexion
              buildInfoRowWithFootnote(
                label: 'Complexion',
                value: _mapSkinTone(_userData['userSkinTone']),
                footnote: 'Cannot be edited! Derived from selfie.',
              ),

              // Hair Colour
              buildInfoRowWithFootnote(
                label: 'Hair Colour',
                value: _mapHairColor(_userData['userHairColor']),
                footnote: 'Cannot be edited! Derived from selfie.',
              ),

              // Eye Colour
              buildInfoRowWithFootnote(
                label: 'Eye Colour',
                value: _mapEyeColor(_userData['userEyeColor']),
                footnote: 'Cannot be edited! Derived from selfie.',
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  // Mapping functions to convert API enum values to readable strings
  String _mapAgeToReadable(String ageEnum) {
    final enumToRangeMap = {
      'AGE_0_20': '< 20',
      'AGE_21_24': '21 to 24',
      'AGE_25_30': '25 to 30',
      'AGE_31_36': '31 to 36',
      'AGE_36_45': '36 to 45', 
      'AGE_46_55': '46 to 55',
      'AGE_55_99': '55+'
    };

    return enumToRangeMap[ageEnum] ?? 'Not specified';
  }

  String _mapGenderToStyleIdentity(String genderEnum) {
    switch (genderEnum) {
      case 'FEMININE':
        return 'Feminine';
      case 'MASCULINE':
        return 'Masculine';
      default:
        return 'Not specified';
    }
  }

  String _mapBodyType(String bodyTypeEnum) {
    switch (bodyTypeEnum) {
      case 'INVERTED_TRIANGLE':
        return 'X Body Type';
      case 'RECTANGLE':
        return 'H Body Type';
      case 'HOURGLASS':
        return 'O Body Type';
      case 'TRIANGLE':
        return 'A Body Type';
      case 'OVAL':
        return 'Y Body Type';
      default:
        return 'Not specified';
    }
  }

  String _mapUndertone(String undertoneEnum) {
    switch (undertoneEnum) {
      case 'WARM':
        return 'Warm';
      case 'COOL':
        return 'Cool';
      case 'NEUTRAL':
        return 'Neutral';
      default:
        return 'Not specified';
    }
  }

  String _mapSkinTone(String skinToneEnum) {
    switch (skinToneEnum) {
      case 'FAIR':
        return 'Fair';
      case 'LIGHT':
        return 'Light';
      case 'MEDIUM':
        return 'Medium';
      case 'DARK':
        return 'Dark';
      case 'DEEP':
        return 'Deep';
      default:
        return 'Not specified';
    }
  }

  String _mapHairColor(String hairColorEnum) {
    switch (hairColorEnum) {
      case 'BLACK':
        return 'Black';
      case 'BROWN':
        return 'Brown';
      case 'BLONDE':
        return 'Blonde';
      case 'RED':
        return 'Red';
      default:
        return 'Not specified';
    }
  }

  String _mapEyeColor(String eyeColorEnum) {
    switch (eyeColorEnum) {
      case 'BROWN':
        return 'Brown';
      case 'BLUE':
        return 'Blue';
      case 'GREEN':
        return 'Green';
      case 'HAZEL':
        return 'Hazel';
      default:
        return 'Not specified';
    }
  }

  // Existing helper methods from the original AboutMeScreen
  Widget buildInfoRow({
    required String label,
    required String value,
    required bool canEdit,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: canEdit ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF1F1F1F),
                fontSize: 14,
                fontFamily: "SatoshiM",
              ),
            ),
            Row(
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    color: Color(0xFF1F1F1F),
                    fontSize: 14,
                    fontFamily: "SatoshiR",
                  ),
                ),
                if (canEdit) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.chevron_right,
                    size: 20,
                    color: Colors.black54,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildInfoRowWithFootnote({
    required String label,
    required String value,
    required String footnote,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Color(0xFF1F1F1F),
                  fontSize: 14,
                  fontFamily: "SatoshiM",
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Color(0xFF1F1F1F),
                  fontSize: 14,
                  fontFamily: "SatoshiR",
                ),
              ),
            ],
          ),
        ),
        // Footnote text properly aligned to the right
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            footnote,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
              fontFamily: "SatoshiR",
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget buildUndertoneRow({
    required String label,
    required String value,
    required bool canEdit,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: canEdit ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF1F1F1F),
                fontSize: 14,
                fontFamily: "SatoshiM",
              ),
            ),
            Row(
              children: [
                // Stack the circles to create overlapping effect
                SizedBox(
                  width: 24, // Width to contain both circles
                  height: 16,
                  child: Stack(
                    children: [
                      // Green dot
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Color(0xFF8BC34A),
                          shape: BoxShape.circle,
                        ),
                      ),
                      // Light green dot, positioned to overlap
                      Positioned(
                        left: 8, // Positive position for overlap
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Color(0xFF9CCC65),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  value,
                  style: const TextStyle(
                    color: Color(0xFF1F1F1F),
                    fontSize: 14,
                    fontFamily: "SatoshiR",
                  ),
                ),
                if (canEdit) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.chevron_right,
                    size: 20,
                    color: Colors.black54,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
