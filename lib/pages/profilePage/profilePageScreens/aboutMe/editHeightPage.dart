import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class EditHeightPage extends StatefulWidget {
  final String initialHeight;
  final Function(String) onHeightUpdated;

  const EditHeightPage({
    Key? key,
    required this.initialHeight,
    required this.onHeightUpdated,
  }) : super(key: key);

  @override
  State<EditHeightPage> createState() => _EditHeightPageState();
}

class _EditHeightPageState extends State<EditHeightPage> {
  late TextEditingController _feetController;
  late TextEditingController _inchesController;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    String heightStr = widget.initialHeight;
    String feet = '';
    String inches = '';

    
    if (heightStr.contains("'") && heightStr.contains("\"")) {
      List<String> parts = heightStr.split("'");
      feet = parts[0].trim();
      inches = parts[1].replaceAll("\"", "").trim();
    }
    
    else if (heightStr.contains("'")) {
      List<String> parts = heightStr.split("'");
      feet = parts[0].trim();
      if (parts.length > 1) {
        inches = parts[1].replaceAll("\"", "").trim();
      }
    }
    // Fallback for other formats
    else {
      List<String> heightParts = heightStr.split(' ');
      feet = heightParts.isNotEmpty
          ? heightParts[0].replaceAll("ft", "").trim()
          : '';
      inches = heightParts.length > 1
          ? heightParts[1].replaceAll("in", "").trim()
          : '';
    }

    _feetController = TextEditingController(text: feet);
    _inchesController = TextEditingController(text: inches);
  }

  @override
  void dispose() {
    _feetController.dispose();
    _inchesController.dispose();
    super.dispose();
  }

  bool get _isButtonEnabled {
    return _feetController.text.isNotEmpty && _inchesController.text.isNotEmpty;
  }

  Future<void> _updateHeight() async {
    if (_isButtonEnabled) {
      setState(() {
        _isUpdating = true;
      });

      try {
        final userId = await SharedPrefsService.getUserId();
        final authToken = await SharedPrefsService.getAuthToken();

        if (userId == null || authToken == null) {
          _showErrorDialog('Authentication failed');
          return;
        }

        // Format the height with proper feet and inches symbols
        String formattedHeight =
            "${_feetController.text}'${_inchesController.text}\"";

        final response = await http.put(
          Uri.parse('${ApiConstants.styleProfileApi}style-profile'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: jsonEncode({
            "userId": userId,
            "updates": {"userHeightMetric": formattedHeight}
          }),
        );

        setState(() {
          _isUpdating = false;
        });

        if (response.statusCode == 200) {
          widget.onHeightUpdated(formattedHeight);
          Navigator.pop(context);
        } else {
          _showErrorDialog('Failed to update height');
        }
      } catch (e) {
        setState(() {
          _isUpdating = false;
        });
        _showErrorDialog('Error updating height: $e');
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Height',
          style: TextStyle(
            color: Color(0xFF1F1F1F),
            fontFamily: "SatoshiM",
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isButtonEnabled && !_isUpdating ? _updateHeight : null,
            child: Text(
              _isUpdating ? 'Updating...' : 'Done',
              style: TextStyle(
                color: _isButtonEnabled && !_isUpdating
                    ? const Color(0xFFE05D38)
                    : Colors.grey,
                fontFamily: "SatoshiM",
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'How tall are you?',
              style: TextStyle(
                fontSize: 18,
                fontFamily: "SatoshiM",
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F1F1F),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFEEEEEE)),
                    ),
                    child: TextField(
                      controller: _feetController,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      cursorColor: const Color(0xFFE05D38),
                      style: const TextStyle(
                        fontFamily: "SatoshiR",
                        fontSize: 16,
                        color: Color(0xFF1F1F1F),
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: '5',
                        hintStyle: TextStyle(
                          color: Color(0xFFAAAAAA),
                          fontFamily: "SatoshiR",
                        ),
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        // LengthLimitedTextInputFormatter(1),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'ft',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 16,
                    color: Color(0xFF1F1F1F),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFEEEEEE)),
                    ),
                    child: TextField(
                      controller: _inchesController,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      cursorColor: const Color(0xFFE05D38),
                      style: const TextStyle(
                        fontFamily: "SatoshiR",
                        fontSize: 16,
                        color: Color(0xFF1F1F1F),
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: '4',
                        hintStyle: TextStyle(
                          color: Color(0xFFAAAAAA),
                          fontFamily: "SatoshiR",
                        ),
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        // LengthLimitedTextInputFormatter(2),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'in',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 16,
                    color: Color(0xFF1F1F1F),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
