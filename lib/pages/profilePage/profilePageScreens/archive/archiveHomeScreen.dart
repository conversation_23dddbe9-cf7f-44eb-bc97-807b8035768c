import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/archive/archiveItems/archivedDetailScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/archive/archiveOutfits/ArchivedOutfitDetailScreen.dart';
import 'dart:convert';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class DetailedApparelItem {
  final String apparelId;
  final String apparelProfile;
  final String apparelType;
  final String colour;
  final String brand;
  final String fit;
  final String pattern;
  final String apparelMediaUrl;
  final String productId;
  final String productName;
  final String wardrobeApparelStatus;
  final String fabric;
  final String length;
  final String transparency;
  final String weave;
  final String occasion;

  DetailedApparelItem({
    required this.apparelId,
    required this.apparelProfile,
    required this.apparelType,
    required this.colour,
    required this.brand,
    required this.fit,
    required this.pattern,
    required this.apparelMediaUrl,
    required this.productId,
    required this.productName,
    this.wardrobeApparelStatus = '',
    this.fabric = '',
    this.length = '',
    this.transparency = '',
    this.weave = '',
    this.occasion = '',
  });

  factory DetailedApparelItem.fromJson(Map<String, dynamic> json) {
    return DetailedApparelItem(
      apparelId: json['apparelId'],
      apparelProfile: json['apparelProfile'],
      apparelType: json['apparelType'],
      colour: json['colour'],
      brand: json['brand'],
      fit: json['fit'],
      pattern: json['pattern'],
      apparelMediaUrl: json['apparelMediaUrl'],
      productId: json['productId'],
      productName: json['productName'],
      wardrobeApparelStatus: json['wardrobeApparelStatus'] ,
      fabric: json['fabric'],
      length: json['length'],
      transparency: json['transparency'],
      weave: json['weave'],
      occasion: json['occasion'],
    );
  }

  // Check if the item is archived
  bool isArchived() {
    return wardrobeApparelStatus.toUpperCase() == 'ARCHIVED';
  }

  // Convert to a Map for ArchivedItemDetailsScreen
  Map<String, dynamic> toMap() {
    return {
      'apparelId': apparelId,
      'image': apparelMediaUrl,
      'name': productName.isNotEmpty ? productName : apparelType,
      'brand': brand,
      'colour': colour,
      'pattern': pattern,
      'fit': fit,
      'fabric': fabric,
      'length': length,
      'transparency': transparency,
      'weave': weave,
      'occasion': occasion,
      'date':
          'Added on ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
      'productId': productId,
      'apparelProfile': apparelProfile,
    };
  }
}

class ArchivedScreen extends StatefulWidget {
  const ArchivedScreen({Key? key}) : super(key: key);

  @override
  State<ArchivedScreen> createState() => _ArchivedScreenState();
}

class _ArchivedScreenState extends State<ArchivedScreen> {
  String _selectedView = 'Archived Items';
  bool _isItemsView = true;
  String _selectedCategory = 'All';
  bool _isLoading = false;
  List<DetailedApparelItem> _archivedItems = [];
  String? _authToken;
  String? _userId;
  List<Outfit> _archivedOutfits = [];
  bool _isOutfitsView = false; // To track if Outfits view is selected

  final List<String> _viewOptions = ['Archived Items', 'Archived Outfits'];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _fetchArchivedOutfits() async {
    if (_authToken == null || _userId == null) return;

    setState(() => _isLoading = true);

    try {
      final uri = Uri.parse(
          '${ApiConstants.outfitApi}outfit/outfits/list?userId=$_userId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Parse archived outfits
        final List<Outfit> allOutfits = (data['data'] as List)
            .map((item) => Outfit.fromJson(item))
            .toList();

        // Filter to include only archived outfits
        final List<Outfit> archivedOutfits = allOutfits
            .where((outfit) => outfit.status.toUpperCase() == 'ARCHIVED')
            .toList();

        // Fetch apparel details for each archived outfit
        for (var outfit in archivedOutfits) {
          await _fetchOutfitApparelDetails(outfit);
        }

        setState(() {
          _archivedOutfits = archivedOutfits;
        });
      } else {
        throw Exception(
            'Failed to load archived outfits: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching archived outfits: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading archived outfits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchOutfitApparelDetails(Outfit outfit) async {
    try {
      for (var apparelId in outfit.apparelPointers) {
        var headers = {'Authorization': 'Bearer $_authToken'};

        var request = http.Request(
            'GET', Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId'));
        request.headers.addAll(headers);

        http.StreamedResponse response = await request.send();

        if (response.statusCode == 200) {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

          Apparel apparel = Apparel.fromJson(jsonResponse);
          outfit.apparels.add(apparel);
        } else {
          print("Error fetching apparel $apparelId: ${response.reasonPhrase}");
        }
      }
    } catch (e) {
      print("Exception when fetching apparel details: $e");
    }
  }

  Future<void> _loadUserData() async {
    try {
      _authToken = await SharedPrefsService.getAuthToken();
      _userId = await SharedPrefsService.getUserId();

      if (_authToken != null && _userId != null) {
        await _fetchArchivedItems(); // Existing method
        await _fetchArchivedOutfits(); // New method
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Authentication error. Please login again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _fetchArchivedItems() async {
    if (_authToken == null || _userId == null) return;

    setState(() => _isLoading = true);

    try {
      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/list?source=USER&sourceId=$_userId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Use DetailedApparelItem to parse the response
        final List<DetailedApparelItem> allItems = (data['data'] as List)
            .map((item) => DetailedApparelItem.fromJson(item))
            .toList();

        // Filter to include only archived items
        final List<DetailedApparelItem> archived =
            allItems.where((item) => item.isArchived()).toList();

        setState(() {
          _archivedItems = archived;
        });
      } else {
        throw Exception(
            'Failed to load archived items: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching archived items: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading archived items: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildDropdownHeader() {
    return PopupMenuButton<String>(
      initialValue: _selectedView,
      onSelected: (String value) {
        setState(() {
          _selectedView = value;
          _isItemsView = value == 'Archived Items';
        });
      },
      offset: const Offset(0, 40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: Color(0xFFE7E7E7), width: 1),
      ),
      color: Colors.white,
      enableFeedback: false,
      // These properties prevent highlighting
      splashRadius: 0,
      tooltip: '',
      elevation: 4,
      padding: EdgeInsets.zero,
      // Custom popup menu item builder to avoid all highlighting
      itemBuilder: (BuildContext context) => _viewOptions.map((String value) {
        return PopupMenuItem<String>(
          value: value,
          enabled: true,
          padding: EdgeInsets.zero,
          height: 40,
          textStyle: TextStyle(color: Colors.transparent),
          // Disable all highlighting effects
          child: Container(
            width: double.infinity,
            height: 40,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: value != _viewOptions.last
                    ? BorderSide(color: Colors.grey.shade200, width: 1)
                    : BorderSide.none,
              ),
            ),
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: "SatoshiM",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        );
      }).toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xffF8F7F7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _selectedView,
              style: const TextStyle(
                fontFamily: "SatoshiM",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 6),
            const Icon(Icons.keyboard_arrow_down,
                color: Colors.black, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryPill(String text, int count, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategory = text;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFF3F3) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Text(
              text,
              style: TextStyle(
                fontFamily: "SatoshiM",
                color: isSelected ? Colors.black : Colors.grey,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFFF4646) : Colors.grey[400],
                shape: BoxShape.circle,
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  fontFamily: "SatoshiM",
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArchivedItemsView() {
    // Calculate category counts
    final categoryCounts = {
      'All': _archivedItems.length,
      'Tops': _archivedItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('top') ||
              item.apparelType.toLowerCase().contains('shirt') ||
              item.apparelType.toLowerCase().contains('tshirt') ||
              item.apparelType.toLowerCase().contains('t-shirt') ||
              item.apparelType.toLowerCase().contains('blouse'))
          .length,
      'Bottoms': _archivedItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('pant') ||
              item.apparelType.toLowerCase().contains('trouser') ||
              item.apparelType.toLowerCase().contains('short') ||
              item.apparelType.toLowerCase().contains('skirt') ||
              item.apparelType.toLowerCase().contains('bottom'))
          .length,
      'Shoes': _archivedItems
          .where((item) =>
              item.apparelType.toLowerCase().contains('shoe') ||
              item.apparelType.toLowerCase().contains('boot') ||
              item.apparelType.toLowerCase().contains('sneaker') ||
              item.apparelType.toLowerCase().contains('sandal'))
          .length,
    };

    return Column(
      children: [
        // Category filters
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCategoryPill('All', categoryCounts['All']!,
                          _selectedCategory == 'All'),
                      _buildCategoryPill('Tops', categoryCounts['Tops']!,
                          _selectedCategory == 'Tops'),
                      _buildCategoryPill('Bottoms', categoryCounts['Bottoms']!,
                          _selectedCategory == 'Bottoms'),
                      _buildCategoryPill('Shoes', categoryCounts['Shoes']!,
                          _selectedCategory == 'Shoes'),
                    ],
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.tune, color: Colors.black54),
                    onPressed: () => showFilterModalSheet(context),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Items grid
        Expanded(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: Color(0xFFF76037)))
              : _archivedItems.isEmpty
                  ? Column(
                      children: [
                        Image.asset(
                          'assets/emptyImages/archive.png',
                          width: 480,
                          height: 480,
                        ),
                        const Center(
                            child: Text('You have no archived items!',
                                style: TextStyle(fontFamily: "SatoshiR"))),
                      ],
                    )
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.82,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: _archivedItems.length,
                      itemBuilder: (context, index) {
                        final item = _archivedItems[index];

                        // Filter by selected category
                        if (_selectedCategory != 'All') {
                          if (_selectedCategory == 'Tops' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('top') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('shirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('tshirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('t-shirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('blouse'))) {
                            return const SizedBox.shrink();
                          }

                          if (_selectedCategory == 'Bottoms' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('pant') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('trouser') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('short') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('skirt') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('bottom'))) {
                            return const SizedBox.shrink();
                          }

                          if (_selectedCategory == 'Shoes' &&
                              !(item.apparelType
                                      .toLowerCase()
                                      .contains('shoe') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('boot') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('sneaker') ||
                                  item.apparelType
                                      .toLowerCase()
                                      .contains('sandal'))) {
                            return const SizedBox.shrink();
                          }
                        }

                        return _buildArchivedItemCard(item);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildArchivedItemCard(DetailedApparelItem item) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ArchivedItemDetailsScreen(
              item: item.toMap(),
              onRestore: _restoreItem,
            ),
          ),
        ).then((_) => _fetchArchivedItems());
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    item.apparelMediaUrl,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey[200],
                      child: const Icon(Icons.image_not_supported,
                          color: Colors.grey),
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 2, 12, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName.isNotEmpty
                        ? item.productName
                        : item.apparelType,
                    style: const TextStyle(
                      fontFamily: "SatoshiM",
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Added on ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                    style: const TextStyle(
                      fontFamily: "SatoshiR",
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _restoreItem(Map<String, dynamic> item) async {
    if (_authToken == null || _userId == null) return false;

    try {
      final apparelId = item['apparelId'];
      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for restoration');
      }

      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/$apparelId?sourceId=$_userId');

      final response = await http.patch(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({"wardrobeApparelStatus": "PRIMARY"}),
      );

      if (response.statusCode == 200) {
        print('Item restored successfully');
        return true;
      } else {
        print(
            'Failed to restore item: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error restoring item: $e');
      return false;
    }
  }

  Widget _buildArchivedOutfitsView() {
    return _isLoading
        ? const Center(
            child: CircularProgressIndicator(color: Color(0xFFF76037)))
        : _archivedOutfits.isEmpty
            ? Column(
                children: [
                  Image.asset(
                    'assets/emptyImages/archive.png',
                    width: 480,
                    height: 480,
                  ),
                  const Center(
                      child: Text('You have no archived outfits!',
                          style: TextStyle(fontFamily: "SatoshiR"))),
                ],
              )
            : ListView.separated(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                itemCount: _archivedOutfits.length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.grey.shade300,
                  thickness: 1,
                ),
                itemBuilder: (context, index) {
                  final outfit = _archivedOutfits[index];
                  return GestureDetector(
                    onTap: () {
                      // Navigate to ArchivedOutfitDetailScreen with outfit data
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ArchivedOutfitDetailScreen(
                            outfit: outfit, // Pass the entire Outfit object
                          ),
                        ),
                      );
                    },
                    child: _buildOutfitCard(outfit),
                  );
                },
              );
  }

  Widget _buildOutfitCard(Outfit outfit) {
    return Container(
      child: Row(
        children: [
          // Outfit Images
          Expanded(
            flex: 2,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: outfit.apparels.isEmpty
                    ? Container(
                        height: 100,
                        color: Colors.grey[200],
                        child: Icon(Icons.broken_image),
                      )
                    : outfit.apparels.length == 1
                        ? AspectRatio(
                            aspectRatio: 1,
                            child: Image.network(
                              outfit.apparels[0].apparelMediaUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Image.asset(
                                'assets/staticImages/cloth.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                          )
                        : outfit.apparels.length == 2
                            ? Column(
                                children: [
                                  // Top image
                                  AspectRatio(
                                    aspectRatio: 2,
                                    child: Image.network(
                                      outfit.apparels[0].apparelMediaUrl,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              Image.asset(
                                        'assets/staticImages/cloth.png',
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Divider(
                                    color: Colors.grey.shade300,
                                    thickness: 1,
                                  ),
                                  // Bottom image
                                  AspectRatio(
                                    aspectRatio: 2,
                                    child: Image.network(
                                      outfit.apparels[1].apparelMediaUrl,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              Image.asset(
                                        'assets/staticImages/cloth.png',
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : outfit.apparels.length == 3
                                ? Column(
                                    children: [
                                      // Top image
                                      AspectRatio(
                                        aspectRatio: 2,
                                        child: Image.network(
                                          outfit.apparels[0].apparelMediaUrl,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Image.asset(
                                            'assets/staticImages/cloth.png',
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      Divider(
                                        color: Colors.grey.shade300,
                                        thickness: 1,
                                      ),
                                      // Bottom row with two images
                                      Row(
                                        children: [
                                          Expanded(
                                            child: AspectRatio(
                                              aspectRatio: 1,
                                              child: Image.network(
                                                outfit.apparels[1]
                                                    .apparelMediaUrl,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error,
                                                        stackTrace) =>
                                                    Image.asset(
                                                  'assets/staticImages/cloth.png',
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                          ),
                                          Container(
                                            width: 1,
                                            height: 50,
                                            color: Colors.grey.shade300,
                                          ),
                                          Expanded(
                                            child: AspectRatio(
                                              aspectRatio: 1,
                                              child: Image.network(
                                                outfit.apparels[2]
                                                    .apparelMediaUrl,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error,
                                                        stackTrace) =>
                                                    Image.asset(
                                                  'assets/staticImages/cloth.png',
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: [
                                      Row(
                                        children: [
                                          _buildOutfitItem(
                                              outfit
                                                  .apparels[0].apparelMediaUrl,
                                              isNetwork: true),
                                          Container(
                                            width: 1,
                                            height: 50,
                                            color: Colors.grey.shade300,
                                          ),
                                          _buildOutfitItem(
                                              outfit
                                                  .apparels[1].apparelMediaUrl,
                                              isNetwork: true),
                                        ],
                                      ),
                                      Divider(
                                        color: Colors.grey.shade300,
                                        thickness: 1,
                                      ),
                                      Row(
                                        children: [
                                          _buildOutfitItem(
                                              outfit
                                                  .apparels[2].apparelMediaUrl,
                                              isNetwork: true),
                                          Container(
                                            width: 1,
                                            height: 50,
                                            color: Colors.grey.shade300,
                                          ),
                                          _buildOutfitItem(
                                              outfit
                                                  .apparels[3].apparelMediaUrl,
                                              isNetwork: true),
                                        ],
                                      ),
                                    ],
                                  ),
              ),
            ),
          ),

          // Outfit Details
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    outfit.outfitName,
                    style: const TextStyle(
                      fontFamily: 'SatoshiM',
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    outfit.description,
                    style: const TextStyle(
                      fontFamily: 'SatoshiR',
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: outfit.tags
                        .map((tag) => _buildTag(tag, const Color(0xFFE6F2FF)))
                        .toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOutfitItem(String imagePath, {bool isNetwork = false}) {
    return Expanded(
      child: AspectRatio(
        aspectRatio: 1,
        child: isNetwork
            ? Image.network(
                imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.broken_image),
                ),
              )
            : Image.asset(
                imagePath,
                fit: BoxFit.cover,
              ),
      ),
    );
  }

  Widget _buildTag(String text, Color backgroundColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'SatoshiR',
          fontSize: 12,
          color: Colors.black87,
        ),
      ),
    );
  }

  void showFilterModalSheet(BuildContext context) {
    // Implement filter modal sheet
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF8F7F7),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: _buildDropdownHeader(),
        centerTitle: true,
      ),
      body: _isItemsView
          ? _buildArchivedItemsView()
          : _buildArchivedOutfitsView(),
    );
  }
}
