import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/pages/authScreens/signIn/loginScreen.dart';
import 'package:lottie/lottie.dart';
import 'package:monova_ai_stylist/services/sharedPrefsService.dart';
import 'package:monova_ai_stylist/widgets/bottomNavBar/bottomNavBar.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isCheckingInternet = true;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Start the animation
    _controller.forward();

    checkInternetAndNavigate();
  }

  Future<bool> checkInternet() async {
  try {
    var connectivityResult = await Connectivity().checkConnectivity();

    if (kIsWeb) {
      // On web, just rely on the connectivity status
      return connectivityResult != ConnectivityResult.none;
    } else {
      // On mobile, also try pinging Google to verify real internet
      if (connectivityResult == ConnectivityResult.none) return false;

      final response = await http.get(Uri.parse('https://clients3.google.com/generate_204'))
          .timeout(const Duration(seconds: 5));
      return response.statusCode == 204;
    }
  } catch (_) {
    return false;
  }
}

  Future checkInternetAndNavigate() async {
    setState(() {
      _isCheckingInternet = true;
    });
    
    bool hasInternet = await checkInternet();
    
    if (hasInternet) {
      // Only navigate if internet is available
      await checkLoginAndNavigate();
    } else {
      setState(() {
        _isCheckingInternet = false;
        _isRetrying = false;
      });
    }
  }

  Future<void> checkLoginAndNavigate() async {
    final isLoggedIn = await SharedPrefsService.isLoggedIn();
    final isFirstTime = await SharedPrefsService.isFirstTimeUser();
    
    return Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => isLoggedIn 
              ? const BottomNavScreen() 
              : isFirstTime
                ? const SignInLoginScreen()
                // ? const OnboardingScreen()
                : const SignInLoginScreen(),
          ),
        );
      }
    });
  }

  void retryWithAnimation() {
    setState(() {
      _isRetrying = true;
    });
    
    // Reset and play the animation again
    _controller.reset();
    _controller.forward();
    
    // Delay the actual network check to allow animation to be visible
    Future.delayed(const Duration(milliseconds: 1500), () {
      checkInternetAndNavigate();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Color(0xFFE05D38)),
    );

    return Scaffold(
      backgroundColor: _isCheckingInternet || _isRetrying 
        ? const Color(0xFFFFF1ED) 
        : Colors.white,
      body: _isCheckingInternet 
        ? _buildSplashScreen()
        : _isRetrying
          ? _buildSplashScreen()
          : _buildNoInternetScreen(),
    );
  }
  
  Widget _buildSplashScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Lottie animation instead of Image asset
          Lottie.asset('assets/splashScreen/logo.json'),
          const SizedBox(height: 20),
          const Text(
            'Monova welcomes you to the',
            style: TextStyle(
              fontFamily: "SatoshiR",
              color: Color(0xFFF76037),
              fontSize: 20,
            ),
          ),
          const Text(
            'Private Beta',
            style: TextStyle(
              fontFamily: "SatoshiR",
              color: Color(0xFFF76037),
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNoInternetScreen() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Circle background with illustration
            Container(
              width: 345,
              height: 248,
              child: Center(
                child: Image.asset(
                  'assets/emptyImages/no_network.png', 
                  // If you don't have this asset, create it or use another icon
                  // You can also use an Icon instead:
                  // child: Icon(Icons.signal_wifi_off, size: 80, color: Color(0xFFE05D38)),
                ),
              ),
            ),
            const SizedBox(height: 32),
            
            // No Network text
            const Text(
              'No Network',
              style: TextStyle(
                fontFamily: "SatoshiM",
                color: Color(0xFF333333),
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Message text
            const Text(
              'Please check your internet connection and try again.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: "SatoshiR",
                color: Color(0xFF666666),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 40),
            
            // Try Again button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: retryWithAnimation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE05D38),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Try Again',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}