import 'package:flutter/material.dart';

class LoaderOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? gifAsset;

  const LoaderOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.gifAsset = 'assets/loader/loading.gif', // Default GIF animation path
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            // color: Colors.black.withOpacity(0.5),
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Image.asset(
                  gifAsset!,
                  width: 100,
                  height: 100,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
      ],
    );
  }
}