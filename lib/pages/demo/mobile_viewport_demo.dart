import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/config/mobile_viewport_config.dart';

class MobileViewportDemo extends StatelessWidget {
  const MobileViewportDemo({super.key});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Mobile Viewport Demo',
          style: TextStyle(
            fontFamily: 'SatoshiM',
            fontSize: 18,
            color: Colors.black,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(
              'Platform Information',
              [
                'Platform: ${kIsWeb ? 'Web' : 'Mobile'}',
                'Screen Width: ${screenSize.width.toStringAsFixed(1)}px',
                'Screen Height: ${screenSize.height.toStringAsFixed(1)}px',
                'Should Show Frame: ${MobileViewportConfig.shouldShowMobileFrame(screenSize.width)}',
              ],
            ),
            const SizedBox(height: 20),
            _buildInfoCard(
              'Mobile Viewport Config',
              [
                'iPhone SE Width: ${MobileViewportConfig.iPhoneSEWidth}px',
                'iPhone Standard Width: ${MobileViewportConfig.iPhoneStandardWidth}px',
                'iPhone Pro Max Width: ${MobileViewportConfig.iPhoneProMaxWidth}px',
                'Frame Radius: ${MobileViewportConfig.frameRadius}px',
                'Mobile Breakpoint: ${MobileViewportConfig.mobileBreakpoint}px',
              ],
            ),
            const SizedBox(height: 20),
            _buildTestSection(),
            const SizedBox(height: 20),
            _buildInstructionsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, List<String> items) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'SatoshiB',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              '• $item',
              style: const TextStyle(
                fontFamily: 'SatoshiR',
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[50]!, Colors.purple[50]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Visual Test Area',
            style: TextStyle(
              fontFamily: 'SatoshiB',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'This colorful section helps you visualize the mobile viewport constraints:',
            style: TextStyle(
              fontFamily: 'SatoshiR',
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.red[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'Red',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'SatoshiM',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'Green',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'SatoshiM',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.orange[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'Orange',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'SatoshiM',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[700]),
              const SizedBox(width: 8),
              const Text(
                'Testing Instructions',
                style: TextStyle(
                  fontFamily: 'SatoshiB',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '1. Open this app in a desktop browser - you should see a phone frame around the content\n\n'
            '2. Resize the browser window to make it smaller - the frame should disappear when width < 600px\n\n'
            '3. Open on a mobile device - no frame should be visible, app uses full width\n\n'
            '4. Check the platform information above to verify detection is working correctly',
            style: TextStyle(
              fontFamily: 'SatoshiR',
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
