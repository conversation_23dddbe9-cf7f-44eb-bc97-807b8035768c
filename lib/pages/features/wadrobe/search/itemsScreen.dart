import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/garmentDetailsScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchItemScreen.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'package:monova_ai_stylist/pages/profilePage/profilePageScreens/favourites/favouriteItems/filterModalSheet.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppState {
  static final List<String> userVibes = [''];

  static void updateVibes(List<String> vibes) {
    if (vibes.isNotEmpty) {
      userVibes.clear();
      userVibes.addAll(vibes);
    }
  }
}

class DetailedApparelItem {
  final String apparelId;
  final String apparelProfile;
  final String apparelType;
  final String colour;
  final String brand;
  final String fit;
  final String pattern;
  final String apparelMediaUrl;
  final String productId;
  final String productName;
  final String wardrobeApparelStatus;
  final String category; // Add this field

  DetailedApparelItem({
    required this.apparelId,
    required this.apparelProfile,
    required this.apparelType,
    required this.colour,
    required this.brand,
    required this.fit,
    required this.pattern,
    required this.apparelMediaUrl,
    required this.productId,
    required this.productName,
    this.wardrobeApparelStatus = '',
    this.category = '', // Initialize with default value
  });

  factory DetailedApparelItem.fromJson(Map<String, dynamic> json) {
    return DetailedApparelItem(
      apparelId: json['apparelId'] ?? '',
      apparelProfile: json['apparelProfile'] ?? '',
      apparelType: json['apparelType'] ?? '',
      colour: json['colour'] ?? '',
      brand: json['brand'] ?? '',
      fit: json['fit'] ?? '',
      pattern: json['pattern'] ?? '',
      apparelMediaUrl: json['apparelMediaUrl'] ?? '',
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      wardrobeApparelStatus: json['wardrobeApparelStatus'] ?? '',
      category: json['category'] ?? '', // Parse from JSON
    );
  }

  // Convert DetailedApparelItem to SearchResult for GarmentDetailsScreen
  SearchResult toSearchResult() {
    return SearchResult(
        id: apparelId,
        title: productName.isNotEmpty ? productName : apparelType,
        imageUrl: apparelMediaUrl,
        price: '',
        brand: brand,
        primaryColor: colour,
        taggedData: {
          "apparelTitle": productName.isNotEmpty ? productName : apparelType,
          "apparelMediaUrl": apparelMediaUrl,
          "apparelId": apparelId,
          "brand": brand,
          "colour": colour,
          "pattern": pattern,
          "fit": fit,
          "type": apparelType,
          "category": category, // Add category to taggedData
          "productId": productId,
          "wardrobeApparelStatus": wardrobeApparelStatus,
        });
  }

  // Check if the item is archived
  bool isArchived() {
    return wardrobeApparelStatus.toUpperCase() == 'ARCHIVED';
  }

  bool isFavorite() {
    return wardrobeApparelStatus.toUpperCase() == 'FAVOURITES';
  }
}

class ItemsScreen extends StatefulWidget {
  const ItemsScreen({Key? key}) : super(key: key);

  @override
  State<ItemsScreen> createState() => _ItemsScreenState();
}

class _ItemsScreenState extends State<ItemsScreen> {
  String selectedCategory = 'All';
  bool _isLoading = false;
  List<DetailedApparelItem> _apparelItems = [];
  Map<String, DetailedApparelItem> _detailedItems = {};
  String? _authToken;
  String? _userId;
  Set<String> _userVibes = {''};
  String? _currentSort; // Add this to track sorting
  late Box apparelItemsBox; // Add Hive box
  final String apparelItemsBoxName = 'apparel_items'; // Box name
  final String lastApparelFetchKey = 'last_apparel_fetch_time';

  @override
  void initState() {
    super.initState();
     _initHive();
  }

  Future<void> _initHive() async {
    try {
      // Initialize Hive
      if (!Hive.isBoxOpen(apparelItemsBoxName)) {
        await Hive.openBox(apparelItemsBoxName);
      }
      apparelItemsBox = Hive.box(apparelItemsBoxName);
      
      // Load auth data and then load items
      await _loadAuthData();
    } catch (e) {
      print("Error initializing Hive for apparel items: $e");
      // Fall back to regular auth data loading if Hive initialization fails
      await _loadAuthData();
    }
  }

  Future<void> _loadAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _authToken = prefs.getString('auth_token');
      _userId = prefs.getString('user_id');
    });

    if (_authToken != null && _userId != null) {
      await _loadApparelItems(); // Use the new caching method
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Authentication error. Please login again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

   Future<void> _loadApparelItems() async {
    setState(() => _isLoading = true);

    try {
      // Check if we have cached data
      if (apparelItemsBox.containsKey('apparel_items_data')) {
        final cachedData = apparelItemsBox.get('apparel_items_data');
        
        // Process cached apparel items
        List<dynamic> rawItems = cachedData['items'] ?? [];
        List<DetailedApparelItem> cachedItems = [];
        
        for (var item in rawItems) {
          if (item is Map) {
            try {
              // Convert cached map to DetailedApparelItem
              DetailedApparelItem apparelItem = DetailedApparelItem(
                apparelId: item['apparelId'] ?? '',
                apparelProfile: item['apparelProfile'] ?? '',
                apparelType: item['apparelType'] ?? '',
                colour: item['colour'] ?? '',
                brand: item['brand'] ?? '',
                fit: item['fit'] ?? '',
                pattern: item['pattern'] ?? '',
                apparelMediaUrl: item['apparelMediaUrl'] ?? '',
                productId: item['productId'] ?? '',
                productName: item['productName'] ?? '',
                wardrobeApparelStatus: item['wardrobeApparelStatus'] ?? '',
                category: item['category'] ?? '',
              );
              
              // Only add non-archived items
              if (!apparelItem.isArchived()) {
                cachedItems.add(apparelItem);
              }
            } catch (e) {
              print("Error parsing cached apparel item: $e");
            }
          }
        }
        
        // Process detailed items as well if available
        Map<dynamic, dynamic> rawDetailedItems = cachedData['detailedItems'] ?? {};
        Map<String, DetailedApparelItem> processedDetailedItems = {};
        
        rawDetailedItems.forEach((key, value) {
          if (value is Map) {
            try {
              String apparelId = key.toString();
              DetailedApparelItem detailedItem = DetailedApparelItem(
                apparelId: value['apparelId'] ?? '',
                apparelProfile: value['apparelProfile'] ?? '',
                apparelType: value['apparelType'] ?? '',
                colour: value['colour'] ?? '',
                brand: value['brand'] ?? '',
                fit: value['fit'] ?? '',
                pattern: value['pattern'] ?? '',
                apparelMediaUrl: value['apparelMediaUrl'] ?? '',
                productId: value['productId'] ?? '',
                productName: value['productName'] ?? '',
                wardrobeApparelStatus: value['wardrobeApparelStatus'] ?? '',
                category: value['category'] ?? '',
              );
              
              // Only add non-archived items
              if (!detailedItem.isArchived()) {
                processedDetailedItems[apparelId] = detailedItem;
              }
            } catch (e) {
              print("Error parsing cached detailed item: $e");
            }
          }
        });
        
        setState(() {
          _apparelItems = cachedItems;
          _detailedItems = processedDetailedItems;
          _isLoading = false;
        });
        
        print("Using cached apparel items data");
        
        // Fetch latest data in background to ensure we're up to date
        _fetchAndUpdateCache();
        return;
      }
      
      // If no cache, fetch from network
      await _fetchApparelItems();
    } catch (e) {
      print("Error loading cached apparel items: $e");
      setState(() {
        _isLoading = false;
      });
      
      // Fallback to network fetch if there's an error with cache
      await _fetchApparelItems();
    }
  }

   Future<void> _fetchAndUpdateCache() async {
    // Fetch in background without showing loading indicators
    try {
      await _fetchApparelItems(showLoading: false);
    } catch (e) {
      print("Background fetch of apparel items failed: $e");
    }
  }

  void _sortItems(String? sortOption) {
    if (sortOption == null) return;

    setState(() {
      _currentSort = sortOption;

      switch (sortOption) {
        case 'Alphabetically (A-Z)':
          _apparelItems.sort((a, b) {
            String nameA =
                a.productName.isNotEmpty ? a.productName : a.apparelType;
            String nameB =
                b.productName.isNotEmpty ? b.productName : b.apparelType;
            return nameA.toLowerCase().compareTo(nameB.toLowerCase());
          });
          break;

        case 'Alphabetically (Z-A)':
          _apparelItems.sort((a, b) {
            String nameA =
                a.productName.isNotEmpty ? a.productName : a.apparelType;
            String nameB =
                b.productName.isNotEmpty ? b.productName : b.apparelType;
            return nameB.toLowerCase().compareTo(nameA.toLowerCase());
          });
          break;

        case 'Oldest Added First':
          // This would require a timestamp field, using apparelId as fallback
          _apparelItems.sort((a, b) => a.apparelId.compareTo(b.apparelId));
          break;

        case 'Oldest Added Last':
          // This would require a timestamp field, using apparelId as fallback
          _apparelItems.sort((a, b) => b.apparelId.compareTo(a.apparelId));
          break;
      }
    });
  }

  Future<SearchResult?> _fetchLatestItemDetails(String apparelId) async {
    if (_authToken == null) return null;

    try {
      final url = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final detailedItem = DetailedApparelItem.fromJson(data['data']);
        return detailedItem.toSearchResult();
      } else {
        print('Failed to fetch latest item details: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching latest item details: $e');
      return null;
    }
  }

Future<void> _toggleFavorite(
      String apparelId, String sourceId, bool isFavorite) async {
    if (_authToken == null) return;

    try {
      final url = Uri.parse(
          '${ApiConstants.apparelApi}apparel/${apparelId}?sourceId=$sourceId');

      final response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode(
            {"wardrobeApparelStatus": isFavorite ? "NORMAL" : "FAVOURITES"}),
      );

      if (response.statusCode == 200) {
        // Success - update local state
        setState(() async {
          if (_detailedItems.containsKey(apparelId)) {
            final updatedItem = DetailedApparelItem(
              apparelId: _detailedItems[apparelId]!.apparelId,
              apparelProfile: _detailedItems[apparelId]!.apparelProfile,
              apparelType: _detailedItems[apparelId]!.apparelType,
              colour: _detailedItems[apparelId]!.colour,
              brand: _detailedItems[apparelId]!.brand,
              fit: _detailedItems[apparelId]!.fit,
              pattern: _detailedItems[apparelId]!.pattern,
              apparelMediaUrl: _detailedItems[apparelId]!.apparelMediaUrl,
              productId: _detailedItems[apparelId]!.productId,
              productName: _detailedItems[apparelId]!.productName,
              wardrobeApparelStatus: isFavorite ? "NORMAL" : "FAVOURITES",
              category: _detailedItems[apparelId]!.category,
            );
            _detailedItems[apparelId] = updatedItem;
            
            // Update cache with the new status
            try {
              final cacheData = apparelItemsBox.get('apparel_items_data');
              if (cacheData != null && cacheData['detailedItems'] != null) {
                Map<dynamic, dynamic> detailedItems = cacheData['detailedItems'];
                if (detailedItems.containsKey(apparelId)) {
                  detailedItems[apparelId]['wardrobeApparelStatus'] = 
                      isFavorite ? "NORMAL" : "FAVOURITES";
                  await apparelItemsBox.put('apparel_items_data', cacheData);
                }
              }
              
              // Also update in the items list
              if (cacheData != null && cacheData['items'] != null) {
                List<dynamic> items = cacheData['items'];
                for (int i = 0; i < items.length; i++) {
                  if (items[i]['apparelId'] == apparelId) {
                    items[i]['wardrobeApparelStatus'] = 
                        isFavorite ? "NORMAL" : "FAVOURITES";
                    break;
                  }
                }
                await apparelItemsBox.put('apparel_items_data', cacheData);
              }
            } catch (e) {
              print('Error updating cache after toggling favorite: $e');
            }
          }
        });

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  isFavorite ? 'Removed from favorites' : 'Added to favorites'),
              backgroundColor: const Color(0xFFF76037),
              duration: const Duration(seconds: 1),
            ),
          );
        }
      } else {
        throw Exception(
            'Failed to update favorite status: ${response.statusCode}');
      }
    } catch (e) {
      print('Error toggling favorite: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorite status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _fetchApparelItems({bool showLoading = true}) async {
    if (showLoading) {
      setState(() => _isLoading = true);
    }

    try {
      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/list?source=USER&userId=$_userId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<DetailedApparelItem> allItems = [];

        // Parse the new JSON structure
        for (var item in data['data']) {
          final apparelData = item['apparel'];
          if (apparelData != null) {
            allItems.add(DetailedApparelItem(
              apparelId: item['apparelId'] ?? '',
              apparelProfile: apparelData['apparelProfile'] ?? '',
              apparelType: apparelData['apparelType'] ?? '',
              colour: apparelData['colour'] ?? '',
              brand: apparelData['brand'] ?? '',
              fit: apparelData['fit'] ?? '',
              pattern: apparelData['pattern'] ?? '',
              apparelMediaUrl: apparelData['apparelMediaUrl'] ?? '',
              productId: apparelData['productId'] ?? '',
              productName: apparelData['productName'] ?? '',
              wardrobeApparelStatus: item['wardrobeApparelStatus'] ?? '',
              category: apparelData['apparelCategory'] ?? '',
            ));
          }
        }

        // Filter out archived items
        final List<DetailedApparelItem> activeItems =
            allItems.where((item) => !item.isArchived()).toList();

        setState(() {
          _apparelItems = activeItems;
        });

        // Cache the items data
        Map<String, dynamic> itemsToCache = {};
        for (var item in activeItems) {
          itemsToCache[item.apparelId] = {
            'apparelId': item.apparelId,
            'apparelProfile': item.apparelProfile,
            'apparelType': item.apparelType,
            'colour': item.colour,
            'brand': item.brand,
            'fit': item.fit,
            'pattern': item.pattern,
            'apparelMediaUrl': item.apparelMediaUrl,
            'productId': item.productId,
            'productName': item.productName,
            'wardrobeApparelStatus': item.wardrobeApparelStatus,
            'category': item.category,
          };
        }
        
        // Prepare data for caching
        Map<String, dynamic> cacheData = {
          'items': activeItems.map((item) => {
            'apparelId': item.apparelId,
            'apparelProfile': item.apparelProfile,
            'apparelType': item.apparelType,
            'colour': item.colour,
            'brand': item.brand,
            'fit': item.fit,
            'pattern': item.pattern,
            'apparelMediaUrl': item.apparelMediaUrl,
            'productId': item.productId,
            'productName': item.productName,
            'wardrobeApparelStatus': item.wardrobeApparelStatus,
            'category': item.category,
          }).toList(),
          'detailedItems': {}, // Will be populated later
          'timestamp': DateTime.now().toIso8601String(),
        };
        
        // Save the cache
        await apparelItemsBox.put('apparel_items_data', cacheData);
        await apparelItemsBox.put(lastApparelFetchKey, DateTime.now().toIso8601String());

        // Fetch detailed info for each non-archived item
        await _fetchDetailedItemsInfo(activeItems);
      } else {
        throw Exception('Failed to load apparel items: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching apparel: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading items: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (showLoading) {
        setState(() => _isLoading = false);
      }
    }
  }
  Future<void> _fetchDetailedItemsInfo(List<DetailedApparelItem> items) async {
    // Use a list instead of a set to keep duplicates
    List<String> vibes = []; // Start with default

    Map<String, DetailedApparelItem> newDetailedItems = {};
    
    for (var item in items) {
      try {
        final response = await http.get(
          Uri.parse('${ApiConstants.apparelApi}apparel/${item.apparelId}'),
          headers: {
            'Authorization': 'Bearer $_authToken',
            'Content-Type': 'application/json',
          },
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final detailedItem = DetailedApparelItem.fromJson(data['data']);

          // Extract vibe if available
          if (data['data'] != null && data['data']['vibe'] != null) {
            final vibe = data['data']['vibe'].toString();
            vibes.add(vibe); // Add to list (allowing duplicates)
            print('Vibe found: $vibe');
          }

          // Only add the item if it's not archived
          if (!detailedItem.isArchived()) {
            newDetailedItems[item.apparelId] = detailedItem;
          }
        }
      } catch (e) {
        print('Error fetching detailed item info for ${item.apparelId}: $e');
      }
    }

    // Update the detailed items in the cache
    if (newDetailedItems.isNotEmpty) {
      try {
        // Get current cache
        final cacheData = apparelItemsBox.get('apparel_items_data') ?? {};
        
        // Convert detailed items to a format that can be cached
        Map<String, dynamic> detailedItemsToCache = {};
        newDetailedItems.forEach((key, value) {
          detailedItemsToCache[key] = {
            'apparelId': value.apparelId,
            'apparelProfile': value.apparelProfile,
            'apparelType': value.apparelType,
            'colour': value.colour,
            'brand': value.brand,
            'fit': value.fit,
            'pattern': value.pattern,
            'apparelMediaUrl': value.apparelMediaUrl,
            'productId': value.productId,
            'productName': value.productName,
            'wardrobeApparelStatus': value.wardrobeApparelStatus,
            'category': value.category,
          };
        });
        
        // Update the cache with detailed items
        cacheData['detailedItems'] = detailedItemsToCache;
        await apparelItemsBox.put('apparel_items_data', cacheData);
      } catch (e) {
        print('Error updating cache with detailed items: $e');
      }
    }

    // Update the vibes and global state
    if (vibes.isNotEmpty) {
      // Convert AppState.userVibes to List for updating
      AppState.userVibes.clear();
      AppState.userVibes.addAll(vibes);

      setState(() {
        _userVibes = Set.from(vibes); // For local state, still need a Set
        _detailedItems = newDetailedItems; // Update the UI with new detailed items
      });
      print('Updated vibes with duplicates: $vibes');
    }
  }
  // Refresh the items list after an item has been archived
  Future<void> _refreshItems() async {
    try {
      // Clear the cache to force a fresh fetch
      await apparelItemsBox.delete('apparel_items_data');
    } catch (e) {
      print('Error clearing apparel items cache: $e');
    }
    await _fetchApparelItems();
  }

  Widget _buildNewStyleCard(DetailedApparelItem item) {
    final detailedItem = _detailedItems[item.apparelId];
    final bool isFavorite = detailedItem?.isFavorite() ?? false;

    return GestureDetector(
      onTap: () async {
        setState(() => _isLoading = true);

        // Fetch latest item details
        final latestDetails = await _fetchLatestItemDetails(item.apparelId);

        // Dismiss loading indicator
        setState(() => _isLoading = false);

        // Navigate to details screen with latest data or fallback to existing data
        if (context.mounted) {
          final searchResult = latestDetails ??
              (detailedItem != null
                  ? detailedItem.toSearchResult()
                  : item.toSearchResult());

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  GarmentDetailsScreen(selectedItem: searchResult),
            ),
          ).then((value) {
            if (value == true) {
              _refreshItems();
            }
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image container with selection border
          Container(
            width: double.infinity,
            height: 160,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            clipBehavior: Clip.hardEdge,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Image
                Image.network(
                  item.apparelMediaUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.image_not_supported,
                        color: Colors.grey),
                  ),
                ),

                // Bookmark icon
                Positioned(
                  top: 8,
                  right: 8,
                  child: InkWell(
                    onTap: () async {
                      await _toggleFavorite(
                          item.apparelId, _userId ?? '', isFavorite);
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Icon(
                        isFavorite ? Icons.bookmark : Icons.bookmark_border,
                        color:
                            isFavorite ? const Color(0xFFF76037) : Colors.grey,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Text outside the container
          const SizedBox(height: 8),

          // Product name
          Text(
            item.productName.isNotEmpty ? item.productName : item.apparelType,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: "SatoshiR",
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 2),

          // Brand and color
          Text(
            [detailedItem?.brand ?? '', detailedItem?.colour ?? '']
                .where((s) => s.isNotEmpty)
                .join(' • '),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12,
              fontFamily: "SatoshiR",
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Count non-archived items by category
    final Map<String, int> categoryCounts = {
      'All': _apparelItems.length,
      'Tops': _apparelItems.where((item) => item.category == 'TOPWEAR').length,
      'Bottoms':
          _apparelItems.where((item) => item.category == 'BOTTOMWEAR').length,
      'Shoes':
          _apparelItems.where((item) => item.category == 'FOOTWEAR').length,
    };

    return LoaderOverlay(
      isLoading: _isLoading,
      gifAsset: 'assets/loader/loading.gif',
      child: Column(
        children: [
          // Category filters
          Container(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Row(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      // ScrollView for categories
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _buildCategoryPill(
                                'All',
                                categoryCounts['All'] ?? 0,
                                selectedCategory == 'All'),
                            _buildCategoryPill(
                                'Tops',
                                categoryCounts['Tops'] ?? 0,
                                selectedCategory == 'Tops'),
                            _buildCategoryPill(
                                'Bottoms',
                                categoryCounts['Bottoms'] ?? 0,
                                selectedCategory == 'Bottoms'),
                            _buildCategoryPill(
                                'Shoes',
                                categoryCounts['Shoes'] ?? 0,
                                selectedCategory == 'Shoes'),
                            // Add extra space at the end to ensure shadow is visible
                            const SizedBox(width: 20),
                          ],
                        ),
                      ),
                      // Gradient shadow overlay
                      Positioned(
                        right: 0,
                        top: 0,
                        bottom: 0,
                        width: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Color(0xffF8f7f7).withOpacity(0.0),
                                Color(0xffF8f7f7).withOpacity(0.7),
                                Color(0xffF8f7f7),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.tune, color: Colors.black54),
                      onPressed: () async {
                        final result = await showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          builder: (context) => SingleChildScrollView(
                            child: Container(
                              padding: EdgeInsets.only(
                                  bottom:
                                      MediaQuery.of(context).viewInsets.bottom),
                              child: FilterModalSheet(),
                            ),
                          ),
                        );

                        if (result != null &&
                            result is Map &&
                            result.containsKey('sortOption')) {
                          _sortItems(result['sortOption']);
                        }
                      },
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Items grid
          Expanded(
            child: _isLoading
                ? const Center()
                : Builder(
                    builder: (context) {
                      // Filter items by selected category first
                      final List<DetailedApparelItem> filteredItems =
                          _apparelItems.where((item) {
                        if (selectedCategory == 'All') {
                          return true;
                        } else if (selectedCategory == 'Tops') {
                          return item.category == 'TOPWEAR';
                        } else if (selectedCategory == 'Bottoms') {
                          return item.category == 'BOTTOMWEAR';
                        } else if (selectedCategory == 'Shoes') {
                          return item.category == 'FOOTWEAR';
                        }
                        return false;
                      }).toList();
                      return GridView.builder(
                        padding: const EdgeInsets.all(16),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.82,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 20,
                        ),
                        itemCount: filteredItems.length +
                            1, // +1 for add new item card
                        itemBuilder: (context, index) {
                          if (index == 0) {
                            return _buildAddNewItemCard();
                          }

                          // Use the filtered item directly
                          final item = filteredItems[index - 1];
                          return _buildNewStyleCard(item);
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddNewItemCard() {
    return InkWell(
      onTap: () => navigateToAddItemPage(context),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: Color(0xffFFF1ED),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.add, color: Color(0xffF76037)),
            ),
            const SizedBox(height: 12),
            const Text(
              'Add new item',
              style: TextStyle(
                fontFamily: "SatoshiM",
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryPill(String text, int count, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCategory = text;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFF3F3) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Text(
              text,
              style: TextStyle(
                fontFamily: "SatoshiM",
                color: isSelected ? Colors.black : Colors.grey,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFFF4646) : Colors.grey[400],
                shape: BoxShape.circle,
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  fontFamily: "SatoshiM",
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void navigateToAddItemPage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddItemPage(),
      ),
    );
  }
}

