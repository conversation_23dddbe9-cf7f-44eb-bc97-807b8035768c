import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/itemsScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class WadrobeHomeScreen extends StatefulWidget {
  const WadrobeHomeScreen({Key? key}) : super(key: key);

  @override
  State<WadrobeHomeScreen> createState() => _WadrobeHomeScreenState();
}

class _WadrobeHomeScreenState extends State<WadrobeHomeScreen> {
  String userName = "";

  @override
  void initState() {
    super.initState();
    _loadUserName();
  }

  Future<void> _loadUserName() async {
    final name = await SharedPrefsService.getFullName();
    setState(() {
      userName = name ?? "User";
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Stack(
          children: [
            Container(color: const Color(0xffF8F7F7)), // Background color
            Column(
              children: [
                // Header with dynamic user name
                Container(
                  width: double.infinity,
                  color: Colors.white.withOpacity(0.9),
                  padding: const EdgeInsets.only(top: 16, bottom: 16),
                  child: Column(
                    children: [
                      Text(
                        "$userName's Wardrobe",
                        style: const TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 30),
                      // Keep the spacing and margin consistent
                      // Container(
                      //   height: 40,
                      //   width: double.infinity,
                      //   margin: const EdgeInsets.symmetric(horizontal: 16),
                      //   decoration: BoxDecoration(
                      //     color: const Color(0xffF8F7F7),
                      //     borderRadius: BorderRadius.circular(30),
                      //   ),
                      // ),
                    ],
                  ),
                ),

                // Content: Only ItemsScreen
                const Expanded(child: ItemsScreen()),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
