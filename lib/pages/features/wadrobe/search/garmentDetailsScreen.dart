import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/categoryEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/colorEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/nameEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/occasionEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/patternEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editGarmentItems/seasonEditPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchItemScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/successScreen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GarmentDetailsScreen extends StatefulWidget {
  final SearchResult selectedItem;
  final bool isFromEditScreen;

  const GarmentDetailsScreen({
    Key? key,
    required this.selectedItem,
    this.isFromEditScreen = false,
  }) : super(key: key);

  @override
  State<GarmentDetailsScreen> createState() => _GarmentDetailsScreenState();
}

class _GarmentDetailsScreenState extends State<GarmentDetailsScreen> {
  bool isExpanded = false;
  bool _isSaving = false;
  bool _isArchiving = false;
  String? _userId;
  String? _authToken;

  @override
  void initState() {
    super.initState();
    _loadUserData().then((_) {
      // Refresh item details after loading user data
      _refreshItemDetails();
    });
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _userId = prefs.getString('user_id');
        _authToken = prefs.getString('auth_token');
      });
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _refreshItemDetails() async {
    if (_userId == null || _authToken == null) return;

    try {
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body)['data'];

        // Update tagged data with latest values
        if (widget.selectedItem.taggedData != null) {
          if (data.containsKey('colour') && data['colour'] != null) {
            widget.selectedItem.taggedData!['colour'] = data['colour'];
            widget.selectedItem.primaryColor = data['colour'];
          }

          if (data.containsKey('pattern') && data['pattern'] != null) {
            widget.selectedItem.taggedData!['pattern'] = data['pattern'];
            widget.selectedItem.pattern = data['pattern'];
          }

          if (data.containsKey('fit') && data['fit'] != null) {
            widget.selectedItem.taggedData!['fit'] = data['fit'];
            widget.selectedItem.fit = data['fit'];
          }

          if (data.containsKey('productName') && data['productName'] != null) {
            widget.selectedItem.taggedData!['productName'] =
                data['productName'];
            widget.selectedItem.title = data['productName'];
          }

          if (data.containsKey('brand') && data['brand'] != null) {
            widget.selectedItem.taggedData!['brand'] = data['brand'];
            widget.selectedItem.brand = data['brand'];
          }

          if (data.containsKey('apparelType') && data['apparelType'] != null) {
            widget.selectedItem.taggedData!['apparelType'] =
                data['apparelType'];
            widget.selectedItem.clothingType = data['apparelType'];
          }

          if (data.containsKey('seasons') && data['seasons'] != null) {
            widget.selectedItem.taggedData!['seasons'] = data['seasons'];
          }

          if (data.containsKey('occasion') && data['occasion'] != null) {
            widget.selectedItem.taggedData!['occasion'] = data['occasion'];
            widget.selectedItem.occasion = data['occasion'];
          }
        }

        // Refresh UI to show latest data
        if (mounted) {
          setState(() {});
        }
      }
    } catch (e) {
      print('Error refreshing item details: $e');
    }
  }

  Future<void> _archiveApparel() async {
    setState(() {
      _isArchiving = true;
    });

    try {
      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for deletion');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the DELETE request URL
      final uri = Uri.parse(
          '${ApiConstants.apparelApi}apparel/$apparelId?sourceId=$sourceId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Make the DELETE request
      final request = http.Request('DELETE', uri);
      request.headers.addAll(headers);

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Archive successful: $responseString');

        // Show success message and navigate back
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Item archived successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(
              context, true); // Return true to indicate successful archive
        }
      } else {
        print('Failed to archive: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to archive apparel. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error archiving apparel: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error archiving item: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isArchiving = false;
        });
      }
    }
  }

  Future<void> _saveApparel() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel');

      // Check if this is an existing item with an apparelId and not from edit screen
      if (!widget.isFromEditScreen &&
          widget.selectedItem.taggedData != null &&
          widget.selectedItem.taggedData!.containsKey('apparelId') &&
          widget.selectedItem.taggedData!['apparelId'].toString().isNotEmpty) {
        // This item is already saved, just show success screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  SuccessScreen(selectedItems: [widget.selectedItem]),
            ),
          );
        }
        return;
      }

      final Map<String, dynamic> apparelMetaData = {
        "apparelProfile":
            widget.selectedItem.taggedData?['apparelProfile'],
        "apparelType": widget.selectedItem.clothingType ??
            widget.selectedItem.taggedData?['apparelCategory'],
        "apparelMediaUrl": widget.selectedItem.imageUrl,
        "pattern": widget.selectedItem.pattern ??
            widget.selectedItem.taggedData?['pattern'],
        "colour": widget.selectedItem.primaryColor,
        "fit": widget.selectedItem.fit ??
            widget.selectedItem.taggedData?['fit'],
        "length": widget.selectedItem.length ??
            widget.selectedItem.taggedData?['length'],
        "fabric": widget.selectedItem.fabric ??
            widget.selectedItem.taggedData?['fabric'],
        "brand": widget.selectedItem.brand,
        "productName": widget.selectedItem.title,
        "productId": widget.selectedItem.id,
        "productUrl": widget.selectedItem.taggedData?['productUrl'] ??
            widget.selectedItem.productUrl,
        "category": widget.selectedItem.taggedData?['category'],
        "transparency": widget.selectedItem.transparency ??
            widget.selectedItem.taggedData?['transparency'],
        "waistRise": widget.selectedItem.waistRise ??
            widget.selectedItem.taggedData?['waistRise'],
        "occasion": widget.selectedItem.occasion ??
            widget.selectedItem.taggedData?['occasion'],
        "weave": widget.selectedItem.weave ??
            widget.selectedItem.taggedData?['weave'],
        "clothingType": widget.selectedItem.clothingType ??
            widget.selectedItem.taggedData?['clothingType'],
      };

      // Remove null values from apparelMetaData
      apparelMetaData.removeWhere((key, value) => value == null);

      print('Saving apparel with metadata: $apparelMetaData');

      // Prepare headers - add auth token if available
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode({
          "source": "USER",
          "sourceId": _userId,
          "apparelMetaData": apparelMetaData
        }),
      );

      if (response.statusCode == 201) {
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  SuccessScreen(selectedItems: [widget.selectedItem]),
            ),
          );
        }
      } else {
        throw Exception(
            'Failed to save apparel. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error saving apparel: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving apparel: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  String getItemTitle() {
    // Use available item data to compose a good title
    final data = widget.selectedItem.taggedData;
    if (data != null) {
      if (data.containsKey('productName') &&
          data['productName'] != null &&
          data['productName'].toString().isNotEmpty) {
        return data['productName'];
      }

      if (data.containsKey('apparelTitle') &&
          data['apparelTitle'] != null &&
          data['apparelTitle'].toString().isNotEmpty) {
        return data['apparelTitle'];
      }
    }

    // If no title in taggedData, construct from available info
    String title = '';

    // Add color if available
    if (widget.selectedItem.primaryColor.isNotEmpty) {
      title += widget.selectedItem.primaryColor;
    }

    // Add item type
    if (widget.selectedItem.clothingType != null &&
        widget.selectedItem.clothingType!.isNotEmpty) {
      title += ' ${widget.selectedItem.clothingType}';
    } else if (widget.selectedItem.title.isNotEmpty) {
      title += ' ${widget.selectedItem.title}';
    }

    // If still empty, return a default title
    return title.isNotEmpty ? title.trim() : 'Apparel Item';
  }

  Map<String, dynamic> getItemDetails() {
    // Extract or provide default values for item details
    final data = widget.selectedItem.taggedData;
    final details = <String, dynamic>{
      'name': getItemTitle(),
      'seasons': 'NA',
      'color': widget.selectedItem.primaryColor.isNotEmpty
          ? widget.selectedItem.primaryColor
          : (data != null && data.containsKey('colour')
              ? data['colour']
              : 'NA'),
      'pattern': widget.selectedItem.pattern ??
          (data != null && data.containsKey('pattern')
              ? data['pattern']
              : 'NA'),
      'category': data != null && data.containsKey('apparelType')
          ? data['apparelType']
          : 'NA',
      'occasions': 'NA',
      'mood': 'NA',
    };

    return details;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    final availableHeight = size.height - padding.top - padding.bottom;
    final itemDetails = getItemDetails();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          getItemTitle(),
          style: TextStyle(
            fontFamily: "SatoshiR",
            color: Colors.black87,
            fontSize: size.width * 0.04,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          _isArchiving
              ? Container(
                  margin: const EdgeInsets.all(8),
                  width: 40,
                  height: 40,
                  child: const CircularProgressIndicator(
                    color: Color(0xFFF76037),
                    strokeWidth: 2,
                  ),
                )
              : IconButton(
                  icon: const Icon(FeatherIcons.archive, color: Colors.black87),
                  onPressed: _archiveApparel,
                ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        child: SizedBox(
          width: double.infinity,
          height: size.height * 0.06,
          child: FloatingActionButton.extended(
            backgroundColor: const Color(0xFFF76037),
            onPressed: _isSaving ? null : _saveApparel,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(size.width * 0.06),
            ),
            label: _isSaving
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Save',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: "SatoshiM",
                      fontSize: size.width * 0.04,
                    ),
                  ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return ListView(
            padding: EdgeInsets.all(size.width * 0.04),
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFEFEFEF), width: 1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Container(
                      height: size.height * 0.45,
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: Image.network(
                          widget.selectedItem.imageUrl,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey[200],
                            child: const Icon(Icons.image_not_supported,
                                color: Colors.grey),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    _buildScoreSection(size),
                  ],
                ),
              ),
              SizedBox(height: size.height * 0.03),
              _buildDetailItem('Name', itemDetails['name']),
              _buildDetailItem('Seasons', itemDetails['seasons'],
                  showArrow: true),
              _buildColorDetailItem(itemDetails['color']),
              _buildPatternDetailItem(itemDetails['pattern']),
              SizedBox(height: size.height * 0.02),
              _buildAdditionalDetails(size),
              if (isExpanded) ...[
                SizedBox(height: size.height * 0.02),
                _buildDetailItem('Category', itemDetails['category'],
                    showArrow: true),
                _buildDetailItem('Occasion', itemDetails['occasions'],
                    showArrow: true),
                _buildDetailItem('Mood', itemDetails['mood'], showArrow: false),
              ],
              SizedBox(height: availableHeight * 0.1),
            ],
          );
        },
      ),
    );
  }

  Widget _buildScoreSection(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.02),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Apparel Score',
                      style: TextStyle(
                        fontFamily: "SatoshiR",
                        color: Colors.black87,
                        fontSize: size.width * 0.045,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFAEBE7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'coming soon',
                        style: TextStyle(
                          fontFamily: "SatoshiR",
                          color: Colors.black54,
                          fontSize: size.width * 0.035,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Learn more',
                    style: TextStyle(
                      fontFamily: "SatoshiR",
                      color: Colors.grey[500],
                      fontSize: size.width * 0.035,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.01,
          ),
          height: size.height * 0.015,
          decoration: BoxDecoration(
            color: const Color(0xFFF0F0F0),
            borderRadius: BorderRadius.circular(14),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 25,
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFD3D3D3),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(14),
                      bottomLeft: Radius.circular(14),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 25,
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFDDDDDD),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(14),
                      bottomRight: Radius.circular(14),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 25,
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFE7E7E7),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(14),
                      bottomRight: Radius.circular(14),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 25,
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF0F0F0),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(14),
                      bottomRight: Radius.circular(14),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: size.height * 0.01),
      ],
    );
  }
  
  Widget _buildDetailItem(String title, String value,
      {bool showArrow = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: InkWell(
        onTap: title == 'Name'
            ? () async {
                // Navigate to NameEditPage and wait for result
                final updatedName = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => NameEditPage(initialName: value),
                  ),
                );

                // If name was updated, update UI and call the update function
                if (updatedName != null && updatedName != value) {
                  _updateApparelName(updatedName);
                }
              }
            : title == 'Seasons'
                ? () async {
                    // Handle navigation to Season edit page
                    final apparelId =
                        widget.selectedItem.taggedData?['apparelId'] ??
                            widget.selectedItem.id;
                    if (apparelId == null || apparelId.toString().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('No apparel ID found for update'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Navigate to SeasonEditPage and wait for result
                    final updatedSeasons = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SeasonEditPage(
                          initialSeasons: value,
                          apparelId: apparelId.toString(),
                          userId: _userId ?? '',
                          authToken: _authToken,
                        ),
                      ),
                    );

                    // If seasons were updated, update UI and call the update function
                    if (updatedSeasons != null && updatedSeasons != value) {
                      _updateApparelSeasons(updatedSeasons);
                    }
                  }
                : title == 'Occasion'
                    ? () async {
                        // Handle navigation to Occasion edit page
                        final apparelId =
                            widget.selectedItem.taggedData?['apparelId'] ??
                                widget.selectedItem.id;
                        if (apparelId == null || apparelId.toString().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('No apparel ID found for update'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Navigate to OccasionEditPage and wait for result
                        final updatedOccasion = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OccasionEditPage(
                              initialOccasion: value,
                              apparelId: apparelId.toString(),
                              userId: _userId ?? '',
                              authToken: _authToken,
                            ),
                          ),
                        );

                        // If occasion was updated, update UI and call the update function
                        if (updatedOccasion != null &&
                            updatedOccasion != value) {
                          _updateApparelOccasion(updatedOccasion);
                        }
                      }
                    : title == 'Category'
                        ? () async {
                            // Handle navigation to Category edit page
                            final apparelId =
                                widget.selectedItem.taggedData?['apparelId'] ??
                                    widget.selectedItem.id;
                            if (apparelId == null ||
                                apparelId.toString().isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content:
                                      Text('No apparel ID found for update'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            // Navigate to CategoryEditPage and wait for result
                            final updatedCategory = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => CategoryEditPage(
                                  initialCategory: value,
                                ),
                              ),
                            );

                            // If category was updated, update UI and call the update function
                            if (updatedCategory != null &&
                                updatedCategory != value) {
                              _updateApparelCategory(updatedCategory);
                            }
                          }
                        : showArrow
                            ? () {
                                // Handle other editable items
                              }
                            : null,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              flex: 2,
              child: Text(
                title,
                style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      value,
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  if (showArrow ||
                      title == 'Name' ||
                      title == 'Seasons' ||
                      title == 'Occasion' ||
                      title == 'Category')
                    const Padding(
                      padding: EdgeInsets.only(left: 4),
                      child: Icon(Icons.chevron_right,
                          color: Colors.grey, size: 20),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateApparelName(String newName) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "productName": newName,
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('colour')) {
          apparelMetaData['colour'] = data['colour'];
        }

        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Name update successful: $responseString');

        // Update local state to reflect the changed name
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['productName'] = newName;
          }
          widget.selectedItem.title = newName;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Name updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update name: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update name. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating name: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating name: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _updateApparelCategory(String newCategory) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "apparelType": newCategory,
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('colour')) {
          apparelMetaData['colour'] = data['colour'];
        }

        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Category update successful: $responseString');

        // Update local state to reflect the changed category
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['apparelType'] = newCategory;
          }
          widget.selectedItem.clothingType = newCategory;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Category updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update category: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update category. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating category: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating category: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _updateApparelOccasion(String newOccasion) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "occasion": newOccasion,
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('colour')) {
          apparelMetaData['colour'] = data['colour'];
        }

        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Occasion update successful: $responseString');

        // Update local state to reflect the changed occasion
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['occasion'] = newOccasion;
          }
          widget.selectedItem.occasion = newOccasion;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Occasion updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update occasion: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update occasion. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating occasion: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating occasion: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _updateApparelSeasons(String newSeasons) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }
      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "seasons": newSeasons,
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('colour')) {
          apparelMetaData['colour'] = data['colour'];
        }

        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Seasons update successful: $responseString');

        // Update local state to reflect the changed seasons
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['seasons'] = newSeasons;
          }
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Seasons updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update seasons: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update seasons. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating seasons: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating seasons: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _updateApparelPattern(String newPattern) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "pattern": newPattern,
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('colour')) {
          apparelMetaData['colour'] = data['colour'];
        }

        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Pattern update successful: $responseString');

        // Update local state to reflect the changed pattern
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['pattern'] = newPattern;
          }
          widget.selectedItem.pattern = newPattern;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Pattern updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update pattern: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update pattern. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating pattern: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating pattern: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Widget _buildColorDetailItem(String color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: InkWell(
        // Add InkWell to make the entire row clickable
        onTap: () async {
          // Navigate to ColorEditPage and wait for result
          final selectedColor = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ColorEditPage(initialColor: color),
            ),
          );

          // If a color was selected, update it via API
          if (selectedColor != null && selectedColor != color) {
            _updateApparelColor(selectedColor);
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Flexible(
              flex: 2,
              child: Text('Colour',
                  style: TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                  overflow: TextOverflow.ellipsis),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: const Color(
                          0xFFB5D1C9), // This should ideally be dynamic based on color name
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  Flexible(
                    child: Text(color,
                        style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(left: 4),
                    child: Icon(Icons.chevron_right, color: Colors.grey, size: 20),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateApparelColor(String newColor) async {
    try {
      // Show loading indicator
      setState(() {
        _isSaving = true;
      });

      // Check if we have an apparel ID from tagged data
      final apparelId = widget.selectedItem.taggedData?['apparelId'] ??
          widget.selectedItem.id;

      if (apparelId == null || apparelId.toString().isEmpty) {
        throw Exception('No apparel ID found for update');
      }

      // Get source ID (user ID)
      final sourceId = _userId;

      // Construct the PUT request URL with the apparel ID
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel/$apparelId');

      // Prepare headers with auth token
      final headers = {
        'Content-Type': 'application/json',
      };

      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }
      // Prepare request body - include existing metadata to maintain other values
      Map<String, dynamic> apparelMetaData = {
        "colour": newColor.toLowerCase(),
      };

      // If there's existing taggedData, include important fields
      if (widget.selectedItem.taggedData != null) {
        final data = widget.selectedItem.taggedData!;

        // Add key fields if they exist
        if (data.containsKey('fit')) {
          apparelMetaData['fit'] = data['fit'];
        }

        if (data.containsKey('forOccasion') || data.containsKey('occasion')) {
          apparelMetaData['forOccasion'] =
              data['forOccasion'] ?? data['occasion'] ?? 'casual';
        }
      }

      // Make the PUT request
      final request = http.Request('PUT', uri);
      request.headers.addAll(headers);
      request.body = jsonEncode({
        "source": "USER",
        "sourceId": sourceId,
        "apparelMetaData": apparelMetaData
      });

      final response = await request.send();
      final responseString = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        print('Color update successful: $responseString');

        // Update local state to reflect the changed color
        setState(() {
          if (widget.selectedItem.taggedData != null) {
            widget.selectedItem.taggedData!['colour'] = newColor;
          }
          widget.selectedItem.primaryColor = newColor;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Color updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print(
            'Failed to update color: ${response.statusCode} - $responseString');
        throw Exception(
            'Failed to update color. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating color: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating color: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Widget _buildPatternDetailItem(String pattern) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE7E7E7), width: 1),
        ),
      ),
      child: InkWell(
        // Add InkWell to make the entire row clickable
        onTap: () async {
          // Navigate to PatternEditPage and wait for result
          final selectedPattern = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PatternEditPage(initialPattern: pattern),
            ),
          );

          // If a pattern was selected, update it via API
          if (selectedPattern != null && selectedPattern != pattern) {
            _updateApparelPattern(selectedPattern);
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Flexible(
              flex: 2,
              child: Text('Pattern',
                  style: TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                  overflow: TextOverflow.ellipsis),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF1F1F1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  Flexible(
                    child: Text(pattern,
                        style: const TextStyle(fontFamily: "SatoshiR", fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(left: 4),
                    child: Icon(Icons.chevron_right, color: Colors.grey, size: 20),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalDetails(Size size) {
    return InkWell(
      onTap: () => setState(() => isExpanded = !isExpanded),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Additional Details',
            style: TextStyle(
              fontFamily: "SatoshiR",
              fontSize: size.width * 0.035,
              fontWeight: FontWeight.w500,
            ),
          ),
          Icon(
            isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: Colors.black87,
            size: size.width * 0.06,
          ),
        ],
      ),
    );
  }
}
