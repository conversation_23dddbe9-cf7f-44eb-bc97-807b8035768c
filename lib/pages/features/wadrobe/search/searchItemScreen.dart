import 'dart:io';
import 'package:any_link_preview/any_link_preview.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/analysisScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/imageCropperScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchPageWidgets/cameraPage.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchPageWidgets/categorizedItemsWidget.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/selectedItemScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class SearchResult {
  final String id;
  String title;
  final String imageUrl;
  final String price;
  String brand;
  String primaryColor;
  final String secondaryColor;
  final String productUrl;
  bool isSelected;
  final File? imageFile;
  final bool isFromLink;
  Map<String, dynamic>? apparelDetails;
  Map<String, dynamic>? taggedData;

  // Add these fields to match the API response
  String? clothingType;
  final String? apparelCategory;
  String? fit;
  String? pattern;
  late final String? fabric;
  final String? length;
  final String? transparency;
  final String? weave;
  String? occasion;
  final String? waistRise;

  SearchResult({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.price,
    this.brand = '',
    this.primaryColor = '',
    this.secondaryColor = '',
    this.productUrl = '',
    this.isSelected = false,
    this.imageFile,
    this.isFromLink = false,
    this.apparelDetails,
    this.taggedData,
    this.clothingType,
    this.apparelCategory,
    this.fit,
    this.pattern,
    this.fabric,
    this.length,
    this.transparency,
    this.weave,
    this.occasion,
    this.waistRise,
  });

  factory SearchResult.fromFile(File file, {String? imageUrl}) {
    return SearchResult(
      id: DateTime.now().toString(),
      title: 'My Item',
      imageUrl: imageUrl ?? '',
      price: '',
      imageFile: file,
      taggedData: {
        'apparelTitle': 'My Item',
        'apparelMediaUrl': imageUrl ?? '',
        'apparelUrl': '',
        'apparelProfile': 'UNKNOWN',
        'apparelCategory': 'UNKNOWN',
        'apparelType': 'unknown',
        'pattern': 'solid',
        'fit': 'regular_fit',
      },
    );
  }

  factory SearchResult.fromLinkPreview(Metadata metadata) {
    return SearchResult(
      id: DateTime.now().toString(),
      title: metadata.title ?? '',
      imageUrl: metadata.image ?? '',
      price: '',
      isFromLink: true,
    );
  }

  factory SearchResult.fromApiResponse(Map<String, dynamic> json) {
  return SearchResult(
    id: json['productId']?.toString() ?? DateTime.now().toString(),
    title: json['productName'] ?? '',
    imageUrl: json['imageUrl'] ?? '',
    price: json['price'] ?? '',
    brand: json['brand'] ?? '',
    primaryColor: json['primaryHumanReadableColor'] ?? '',
    secondaryColor: json['secondaryHumanReadableColor'] ?? '',
    productUrl: json['productUrl'] ?? '',
    // Store all API response data directly
    taggedData: json,
    // Extract relevant properties for easy access
    clothingType: json['clothingType'],
    apparelCategory: json['apparelCategory'],
    fit: json['fit'],
    pattern: json['pattern'],
    fabric: json['fabric'],
    length: json['length'],
    transparency: json['transparency'],
    weave: json['weave'],
    occasion: json['occasion'],
    waistRise: json['waistRise'],
  );
}
}

class AddItemPage extends StatefulWidget {
  const AddItemPage({Key? key}) : super(key: key);

  @override
  State<AddItemPage> createState() => _AddItemPageState();
}

class _AddItemPageState extends State<AddItemPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<SearchResult> _searchResults = [];
  List<SearchResult> _selectedItems = []; // Track selected items
  bool _isLoading = false;
  bool _hasSelection = false;
  final ImagePicker _picker = ImagePicker();
  bool _isValidUrl = false;
  bool _hasSearched = false; // Track if user has searched
  SearchResult? _linkPreviewResult;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
    _searchController.addListener(_onSearchChange);
  }

  void _onFocusChange() {
    // No longer need to track fullscreen state as this is a full page
  }

  void _onSearchChange() {
    final text = _searchController.text.trim();
    if (text.isNotEmpty) {
      bool isUrl = text.toLowerCase().startsWith('http://') ||
          text.toLowerCase().startsWith('https://');

      setState(() {
        _isValidUrl = isUrl;
      });
    } else {
      setState(() {
        _isValidUrl = false;
        _hasSearched = false; // Reset search state when search is cleared
      });
    }
  }

  void _toggleSelection(SearchResult item) {
    setState(() {
      // Find if the item is already selected
      final existingIndex =
          _selectedItems.indexWhere((selected) => selected.id == item.id);

      if (existingIndex >= 0) {
        // If already selected, unselect it
        _selectedItems.removeAt(existingIndex);
        item.isSelected = false;
      } else {
        // Allow multiple selections - just add the new item
        _selectedItems.add(item);
        item.isSelected = true;
      }

      // Update hasSelection flag
      _hasSelection = _selectedItems.isNotEmpty;
    });
  }

  void _clearAllSelections() {
    setState(() {
      // Clear all selections
      for (var item in _selectedItems) {
        item.isSelected = false;
      }
      _selectedItems.clear();
      _hasSelection = false;
      _searchController.clear();
      _hasSearched = false;
      _searchResults = [];
    });
  }

  void _handleLinkPreviewClick(SearchResult result) async {
    // Show loading screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AnalysisScreen(selectedItem: result),
      ),
    );

    try {
      // Create tagged data from the link preview data
      final taggedResult = SearchResult(
        id: DateTime.now().toString(),
        title: result.title,
        imageUrl: result.imageUrl,
        price: '',
        isFromLink: true,
        taggedData: {
          'apparelTitle': result.title,
          'apparelMediaUrl': result.imageUrl,
          'apparelUrl': _searchController.text.trim(),
          'type':'',
          'category': '',
          'details': {
            'fit': '',
            'pattern': '',
            'fabric': '',
          }
        },
      );

      if (!mounted) return;

      // Replace the analysis screen with selected item screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) =>
              SelectedItemScreen(selectedItems: [taggedResult]),
        ),
      );
    } catch (e) {
      print('Error details in _handleLinkPreviewClick: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing URL: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Go Back',
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      );
    }
  }

  Future<String> _getApparelProfile() async {
    try {
      final userId = await SharedPrefsService.getUserId();
      final authToken = await SharedPrefsService.getAuthToken();

      if (userId == null || authToken == null) return '';

      final response = await http.get(
        Uri.parse(
            '${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        print("Style Profile API Response: $jsonResponse"); // Debug log

        if (jsonResponse['data'] != null &&
            jsonResponse['data']['userGender'] != null) {
          final gender =
              jsonResponse['data']['userGender'].toString().toUpperCase();
          if (gender == 'MASCULINE') {
            return 'Men';
          } else if (gender == 'FEMININE') {
            return 'Women';
          }
        }
      }
    } catch (e) {
      print('Error getting apparel profile: $e');
    }

    return '';
  }

  Future<void> _performSearch() async {
    if (_searchController.text.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apparelProfile = await _getApparelProfile();
      print("profile: $apparelProfile");
      final query = _searchController.text.trim().replaceAll(' ', '+');

      // New API endpoint
      final url = Uri.parse(
          '${ApiConstants.apparelCollectionApi}apparels/search?query=$query&apparelProfile=$apparelProfile');

      print('Search URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['data'] != null && data['data'] is List) {
          final results = (data['data'] as List).map((item) {
            return SearchResult.fromApiResponse(item);
          }).toList();

          setState(() {
            _searchResults = results;
          });
        } else {
          setState(() {
            _searchResults = [];
          });
        }
      } else {
        print('Error: ${response.statusCode}');
        setState(() {
          _searchResults = [];
        });
      }
    } catch (e) {
      print('Error performing search: $e');
      setState(() {
        _searchResults = [];
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildLinkPreview() {
    return AnyLinkPreview.builder(
      link: _searchController.text,
      itemBuilder: (context, metadata, imageProvider, error) {
        if (error != null) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.error_outline, color: Colors.grey),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Link not found!',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          fontFamily: "SatoshiR",
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Retry with a new link.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontFamily: "SatoshiR",
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        if (metadata != null) {
          _linkPreviewResult = SearchResult.fromLinkPreview(metadata);

          return GestureDetector(
            onTap: () {
              if (_linkPreviewResult != null) {
                _handleLinkPreviewClick(_linkPreviewResult!);
              }
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE7E7E7)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (imageProvider != null)
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          bottomLeft: Radius.circular(12),
                        ),
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (metadata.title != null)
                            Text(
                              metadata.title!,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                fontFamily: "SatoshiR",
                              ),
                            ),
                          const SizedBox(height: 4),
                          if (metadata.desc != null)
                            Text(
                              metadata.desc!,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey[600],
                                fontFamily: "SatoshiR",
                              ),
                            ),
                          const SizedBox(height: 8),
                          Text(
                            Uri.tryParse(metadata.url ?? '')?.host ?? '',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontFamily: "SatoshiR",
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const Center(
            child: CircularProgressIndicator(color: Color(0xFFF76037)));
      },
      cache: const Duration(hours: 1),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _openCameraScreen() async {
    try {
      // Get available cameras
      final cameras = await availableCameras();

      // Launch camera screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CameraScreen(cameras: cameras),
        ),
      );
    } catch (e) {
      print('Error accessing camera: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        toolbarHeight: 60,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Add Items',
          style: TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _clearAllSelections,
            child: const Text(
              'Clear All',
              style: TextStyle(
                decoration: TextDecoration.underline,
                fontFamily: "SatoshiR",
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar with camera button as shown in the image
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Search bar (expanded to take available space)
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF8F7F7),
                      borderRadius: BorderRadius.circular(30),
                      border: Border.all(color: const Color(0xffE7E7E7)),
                    ),
                    child: TextField(
                      cursorColor: const Color(0xFFF76037),
                      controller: _searchController,
                      focusNode: _focusNode,
                      onSubmitted: (_) {
                        _performSearch();
                        setState(() {
                          _hasSearched = true;
                        });
                      },
                      decoration: const InputDecoration(
                        hintText: 'Search items',
                        hintStyle:
                            TextStyle(fontFamily: "SatoshiR", fontSize: 16),
                        prefixIcon:
                            Icon(FeatherIcons.search, color: Colors.black),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 15,
                        ),
                      ),
                    ),
                  ),
                ),
                // Camera button
                const SizedBox(width: 12),
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: const Color(0xffE7E7E7)),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.camera_alt_outlined),
                    onPressed: _openCameraScreen,
                  ),
                ),
              ],
            ),
          ),

          // Main content area
          Expanded(
            child: _isValidUrl
                ? _buildLinkPreview()
                : _hasSearched
                    ? _buildSearchResults()
                    : CategorizedItemsWidget(
                        onItemSelected: _toggleSelection,
                        onClearAll: _clearAllSelections,
                        selectedItems: _selectedItems,
                      ),
          ),

          // Bottom button (only show when there's a selection)
          if (_hasSelection)
            Container(
              padding: const EdgeInsets.all(16),
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedItems.isNotEmpty
                    ? () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SelectedItemScreen(
                                selectedItems: _selectedItems),
                          ),
                        );
                      }
                    : null, // Disabled when no items selected
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF76037),
                  disabledBackgroundColor: Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  elevation: 0,
                ),
                child: Text(
                  'Add items (${_selectedItems.length})',
                  style: TextStyle(
                    color: _selectedItems.isNotEmpty
                        ? Colors.white
                        : Colors.grey[500],
                    fontSize: 16,
                    fontFamily: "SatoshiR",
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFF76037)),
      );
    } else if (_searchResults.isEmpty) {
      return _buildNoResults();
    } else {
      return GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _searchResults.length,
        itemBuilder: (context, index) {
          final result = _searchResults[index];
          return GestureDetector(
            onTap: () => _toggleSelection(result),
            child: _buildNewStyleCard(result),
          );
        },
      );
    }
  }

  // New design card based on the image
  Widget _buildNewStyleCard(SearchResult result) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image container with selection border
        Container(
          width: double.infinity,
          height: 160,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: result.isSelected
                  ? const Color(0xFFF76037)
                  : Colors.transparent,
              width: result.isSelected ? 2 : 0,
            ),
          ),
          clipBehavior: Clip.hardEdge,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Image
              result.imageUrl.isNotEmpty
                  ? Image.network(
                      result.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported,
                            color: Colors.grey),
                      ),
                    )
                  : Container(
                      color: Colors.grey[200],
                      child: const Icon(Icons.image_not_supported,
                          color: Colors.grey),
                    ),

              // Selected indicator
              if (result.isSelected)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Color(0xFFF76037),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),

              // Add the empty circle for unselected items
              if (!result.isSelected)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Text outside the container
        const SizedBox(height: 8),

        // Product name
        Text(
          result.title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 14,
            fontFamily: "SatoshiR",
            color: Colors.black,
          ),
        ),

        const SizedBox(height: 2),

        // Brand and color
        if (result.brand.isNotEmpty || result.primaryColor.isNotEmpty)
          Text(
            [
              if (result.brand.isNotEmpty) result.brand,
              if (result.primaryColor.isNotEmpty) result.primaryColor
            ].join(' • '),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12,
              fontFamily: "SatoshiR",
              color: Colors.grey[600],
            ),
          ),
      ],
    );
  }

  Widget _buildNoResults() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Unable to find what\nyou are looking for?',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Try making your search more specific, like is it dark or light blue? does it have any design on it?',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: "SatoshiR",
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

// Usage example:
void navigateToAddItemPage(BuildContext context) {
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => const AddItemPage(),
    ),
  );
}