import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:crop_your_image/crop_your_image.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:azblob/azblob.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/analysisScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchItemScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/selectedItemScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ImageCropperScreen extends StatefulWidget {
  final File imageFile;
  final List<File>? additionalImages;

  const ImageCropperScreen({
    Key? key,
    required this.imageFile,
    this.additionalImages,
  }) : super(key: key);

  @override
  State<ImageCropperScreen> createState() => _ImageCropperScreenState();
}

class _ImageCropperScreenState extends State<ImageCropperScreen> {
  final _cropController = CropController();
  bool _isCropping = false;
  Uint8List? _croppedData;
  List<File> _allImages = [];
  int _currentImageIndex = 0;
  List<String> _uploadedImageUrls = [];
  bool _processingAllImages = false;
  
  @override
  void initState() {
    super.initState();
    _setupImagesList();
  }

  void _setupImagesList() {
    _allImages = [widget.imageFile];
    if (widget.additionalImages != null && widget.additionalImages!.isNotEmpty) {
      _allImages.addAll(widget.additionalImages!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Crop Item (${_currentImageIndex + 1}/${_allImages.length})',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontFamily: "SatoshiR",
          ),
        ),
        centerTitle: true,
      ),
      body: _processingAllImages
          ? _buildProcessingScreen()
          : FutureBuilder<Uint8List>(
              future: _allImages[_currentImageIndex].readAsBytes(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(
                    child: CircularProgressIndicator(color: Color(0xFFF76037)),
                  );
                }

                return Stack(
                  children: [
                    Crop(
                      controller: _cropController,
                      image: snapshot.data!,
                      onCropped: (result) {
                        switch (result) {
                          case CropSuccess(:final croppedImage):
                            _handleCroppedImage(croppedImage);
                          case CropFailure(:final cause):
                            _showErrorDialog(cause.toString());
                        }
                        setState(() => _isCropping = false);
                      },
                      maskColor: Colors.black.withOpacity(0.8),
                      cornerDotBuilder: (size, edgeAlignment) => _buildCornerDot(),
                      interactive: true,
                      fixCropRect: false,
                      radius: 0,
                      baseColor: Colors.black,
                    ),
                    // Bottom controls
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        color: Colors.black.withOpacity(0.5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Skip button
                            if (_allImages.length > 1)
                              SizedBox(
                                width: 80,
                                height: 50,
                                child: TextButton(
                                  onPressed: _isCropping ? null : _skipCurrentImage,
                                  style: TextButton.styleFrom(
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text(
                                    'Skip',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontFamily: "SatoshiR",
                                    ),
                                  ),
                                ),
                              )
                            else
                              const SizedBox(width: 80),
                            
                            // Done button
                            SizedBox(
                              width: 150,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: _isCropping
                                    ? null
                                    : () {
                                        setState(() => _isCropping = true);
                                        _cropController.crop();
                                      },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFFF76037),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                  elevation: 0,
                                ),
                                child: _isCropping
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.white,
                                        ),
                                      )
                                    : Text(
                                        _isLastImage() ? 'Done' : 'Next',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: "SatoshiR",
                                        ),
                                      ),
                              ),
                            ),
                            
                            // Balance layout
                            const SizedBox(width: 80),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
    );
  }

  Widget _buildProcessingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: Color(0xFFF76037)),
          const SizedBox(height: 20),
          Text(
            'Processing ${_uploadedImageUrls.length}/${_allImages.length} images...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: "SatoshiR",
            ),
          ),
        ],
      ),
    );
  }

  bool _isLastImage() {
    return _currentImageIndex >= _allImages.length - 1;
  }

  void _skipCurrentImage() {
    if (_currentImageIndex < _allImages.length - 1) {
      setState(() {
        _currentImageIndex++;
      });
    } else {
      _finishAllImages();
    }
  }

  Future<void> _handleCroppedImage(Uint8List croppedImage) async {
    try {
      // Save cropped image to temporary file
      final tempDir = await Directory.systemTemp.createTemp();
      final tempFile = File('${tempDir.path}/cropped_image_${_currentImageIndex}.jpg');
      await tempFile.writeAsBytes(croppedImage);

      // Upload to Azure Blob Storage
      final String? azureImageUrl = await _uploadImageToAzure(croppedImage);

      if (azureImageUrl == null) {
        throw Exception("Failed to upload image to Azure");
      }

      print('Uploaded image URL: $azureImageUrl');
      
      // Add to uploaded URLs
      _uploadedImageUrls.add(azureImageUrl);

      if (_isLastImage()) {
        _finishAllImages();
      } else {
        // Move to next image
        setState(() {
          _currentImageIndex++;
        });
      }
    } catch (e) {
      print('Error in image processing: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _finishAllImages() async {
    if (_uploadedImageUrls.isEmpty) {
      Navigator.of(context).pop(); // No images to process
      return;
    }
    
    setState(() {
      _processingAllImages = true;
    });

    try {
    // Create initial SearchResults for all images
    List<SearchResult> initialItems = [];
    
    for (int i = 0; i < _allImages.length; i++) {
      if (i < _uploadedImageUrls.length) {
        final tempDir = await Directory.systemTemp.createTemp();
        final tempFile = File('${tempDir.path}/image_$i.jpg');
        await tempFile.writeAsBytes(await _allImages[i].readAsBytes());
        
        initialItems.add(SearchResult.fromFile(tempFile, imageUrl: _uploadedImageUrls[i]));
      }
    }

      // Show analysis screen while uploading and processing
      if (!mounted) return;

      Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AnalysisScreen(
          selectedItem: initialItems.first,
          additionalItems: initialItems.length > 1 ? initialItems.sublist(1) : null,
        ),
      ),
    );

      // Get gender from preferences for API call
      final gender = await _getUserGender();

      // Process all uploaded images
      final List<SearchResult> allItems = [];
      
      for (String imageUrl in _uploadedImageUrls) {
        try {
          // Call the search API for each image
          final responseData = await _searchByImage(imageUrl, gender);
          
          // Create SearchResult from response
          SearchResult item = _createSearchResultFromResponse(responseData, imageUrl, gender);
          allItems.add(item);
        } catch (e) {
          print('Error processing image $imageUrl: $e');
          // Create basic item for failed images
          final basicItem = SearchResult(
            id: DateTime.now().toString(),
            title: 'My Item',
            imageUrl: imageUrl,
            price: '',
            taggedData: {
              'apparelTitle': '',
              'apparelMediaUrl': imageUrl,
              'apparelUrl': '',
              'apparelProfile': gender,
              'apparelCategory': '',
              'apparelType': '',
              'pattern': '',
              'fit': '',
            },
          );
          allItems.add(basicItem);
        }
      }

      // Navigate to the selected item screen with all processed items
      if (!mounted) return;

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => SelectedItemScreen(selectedItems: allItems),
        ),
      );
    } catch (e) {
      print('Error in final processing: $e');
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  SearchResult _createSearchResultFromResponse(
      Map<String, dynamic> responseData, String imageUrl, String gender) {
    
    if (responseData['results'] != null &&
        responseData['results']['valid'] == true) {
      final results = responseData['results'];

      // Extract attributes from the response
      final attributes = results['attributes'] as Map<String, dynamic>;

      if (results['items'] != null && (results['items'] as List).isNotEmpty) {
        // Use the first search result item
        final firstItem = (results['items'] as List).first;
        return SearchResult.fromApiResponse(firstItem);
      } else {
        // Create a new item with the analyzed attributes
        return SearchResult(
          id: DateTime.now().toString(),
          title: attributes['apparelType']?.toString().replaceAll('_', ' ') ??
              'My Item',
          imageUrl: attributes['imageUrl'] ?? imageUrl,
          price: '',
          taggedData: {
            'apparelTitle':
                attributes['apparelType']?.toString().replaceAll('_', ' '),
            'apparelMediaUrl': imageUrl,
            'apparelUrl': '',
            'apparelProfile': attributes['apparelProfile'] ?? gender,
            'apparelCategory': attributes['apparelCategory'],
            'apparelType': attributes['apparelType'],
            'pattern': attributes['pattern'],
            'fit': attributes['fit'],
            'fabric': attributes['fabric'],
            'length': attributes['ankleLength'],
            'fastening': attributes['fastening'],
            'toeType': attributes['toeType'],
          },
        );
      }
    } else {
      // If API doesn't return valid results, create a basic item
      return SearchResult(
        id: DateTime.now().toString(),
        title: 'My Item',
        imageUrl: imageUrl,
        price: '',
        taggedData: {
          'apparelTitle': '',
          'apparelMediaUrl': imageUrl,
          'apparelUrl': '',
          'apparelProfile': gender,
          'apparelCategory': '',
          'apparelType': '',
          'pattern': '',
          'fit': '',
        },
      );
    }
  }

  Widget _buildCornerDot() {
    return Container(
      height: 24,
      width: 24,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: const Center(
        child: Icon(
          Icons.drag_handle,
          color: Colors.black,
          size: 14,
        ),
      ),
    );
  }

  Future<String?> _uploadImageToAzure(Uint8List imageBytes) async {
    // This method unchanged
    try {
      final storage = AzureStorage.parse(
          "DefaultEndpointsProtocol=https;AccountName=monovaoutfits;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net");
      const containerName = "apparels-dev";

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${DateTime.now().microsecond}-$timestamp.jpg';
      final blobPath = '/$containerName/$fileName';

      await storage.putBlob(
        blobPath,
        bodyBytes: imageBytes,
        contentType: 'image/jpeg',
      );

      final imageUrl = 'https://monovaoutfits.blob.core.windows.net$blobPath';
      print('Uploaded image URL: $imageUrl');

      return imageUrl;
    } catch (e) {
      print('Error uploading image to Azure: $e');
      return null;
    }
  }

  Future<String> _getUserGender() async {
  try {
    final userId = await SharedPrefsService.getUserId();
    final authToken = await SharedPrefsService.getAuthToken();

    if (userId == null || authToken == null) return '';

    final response = await http.get(
      Uri.parse('${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);

      print("Style Profile API Response: $jsonResponse"); // Debug log

      if (jsonResponse['data'] != null &&
          jsonResponse['data']['userGender'] != null) {
        final gender = jsonResponse['data']['userGender'].toString().toLowerCase();
        if (gender == 'masculine') {
          return 'MALE';
        } else if (gender == 'feminine') {
          return 'FEMALE';
        }
      }
    }

    return '';
  } catch (e) {
    print('Error getting gender: $e');
    return '';
  }
}

  Future<Map<String, dynamic>> _searchByImage(
      String imageUrl, String gender) async {
    // This method unchanged
    try {
      final authToken = await SharedPrefsService.getAuthToken();

      if (authToken == null) {
        throw Exception("Authentication token not found");
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.searchApi}/search'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken'
        },
        body: json.encode({"imageUrl": imageUrl, "gender": gender}),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to search by image: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error in search API call: $e');
      throw e;
    }
  }

  void _showErrorDialog(String cause) {
    // This method unchanged
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text('Failed to crop image: $cause'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
