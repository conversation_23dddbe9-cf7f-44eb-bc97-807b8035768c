import 'dart:async';
import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/CollectionDetailScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/NewOutfitScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/itemsScreen.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/services/weatherService.dart';
import 'package:intl/intl.dart';

class Outfit {
  final String userId;
  final String outfitId;
  final String outfitName;
  final List<String> apparelPointers;
  final String dateCreated;
  final String status;
  final List<String> tags;
  List<Apparel> apparels = [];
  String description;

  Outfit({
    required this.userId,
    required this.outfitId,
    required this.outfitName,
    required this.apparelPointers,
    required this.dateCreated,
    required this.status,
    required this.tags,
    this.description = '',
  });

  factory Outfit.fromJson(Map<String, dynamic> json) {
    return Outfit(
      userId: json['userId'],
      outfitId: json['outfitId'],
      outfitName: json['outfitName'],
      apparelPointers: List<String>.from(json['apparelPointers']),
      dateCreated: json['dateCreated'],
      status: json['status'],
      tags: List<String>.from(json['tags']),
    );
  }
}

class Apparel {
  final String apparelId;
  final String apparelType;
  final String apparelMediaUrl;
  final String productName;
  final String brand;
  final String colour;

  Apparel({
    required this.apparelId,
    required this.apparelType,
    required this.apparelMediaUrl,
    required this.productName,
    required this.brand,
    required this.colour,
  });

factory Apparel.fromJson(Map<String, dynamic> json) {
  return Apparel(
    apparelId: json['data']['apparelId'] ?? '',
    apparelType: json['data']['apparelType'] ?? '',
    apparelMediaUrl: json['data']['apparelMediaUrl'] ?? '',
    productName: json['data']['productName'] ?? '',
    brand: json['data']['brand'] ?? '', 
    colour: json['data']['colour'] ?? '',
  );
}
}

class OutfitCollection {
  final String userId;
  final String outfitCollectionId;
  final String outfitCollectionName;
  final String outfitCollectionMediaUrl;
  final List<String> outfitPointers;
  final String dateCreated;

  OutfitCollection({
    required this.userId,
    required this.outfitCollectionId,
    required this.outfitCollectionName,
    required this.outfitCollectionMediaUrl,
    this.outfitPointers = const [],
    this.dateCreated = '',
  });

  factory OutfitCollection.fromJson(Map<String, dynamic> json) {
    return OutfitCollection(
      userId: json['userId'],
      outfitCollectionId: json['outfitCollectionId'],
      outfitCollectionName: json['outfitCollectionName'],
      outfitCollectionMediaUrl: json['outfitCollectionMediaUrl'],
      outfitPointers: json['outfitPointers'] != null
          ? List<String>.from(json['outfitPointers'] is String
              ? [json['outfitPointers']]
              : json['outfitPointers'])
          : [],
      dateCreated: json['dateCreated'],
    );
  }
}

List<OutfitCollection> collections = [];
bool isCollectionLoading = false;
Outfit? currentOutfit;

class OutfitsScreen extends StatefulWidget {
  const OutfitsScreen({Key? key}) : super(key: key);

  @override
  State<OutfitsScreen> createState() => _OutfitsScreenState();
}

class _OutfitsScreenState extends State<OutfitsScreen> {
  // List to store outfit data
  List<Outfit> outfits = [];
  bool isLoading = true;
  TextEditingController newCollectionController = TextEditingController();
  List<String> _userVibes = [];
  String? _userGender;

  late Box outfitsBox;
  final String outfitsBoxName = 'outfits_screen_cache';
  final String lastOutfitsFetchKey = 'last_outfits_fetch_time';
  final String midnightClearKey = 'last_midnight_clear_time';
  Timer? _midnightTimer;
  final Duration cacheValidDuration = Duration(hours: 12); // Cache valid for 12 hours

  final List<String> description = [
    "A perfect day out outfit for the chilly weather.",
    "Great for office meetings and professional settings.",
    "Ideal for casual Fridays and relaxed work environments.",
    "Perfect ensemble for important presentations."
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check if vibes have been updated
    if (_userVibes.toString() != AppState.userVibes.toString()) {
      setState(() {
        _userVibes = List.from(AppState.userVibes);
      });
      _clearCacheAndFetch(); // Clear cache when vibes change
    }
  }

  @override
  void initState() {
    super.initState();
    _userVibes = List.from(AppState.userVibes);
    _initHive();
    fetchCollections();
    _fetchUserGender();
    // Start the timer to check for midnight reset
    _setupMidnightTimer();
  }


@override
  void dispose() {
    _midnightTimer?.cancel();
    newCollectionController.dispose();
    super.dispose();
  }

  void setVibes(Set<String> vibes) {
    if (vibes.isNotEmpty) {
      setState(() {
        _userVibes = List.from(vibes);
      });
    }
  }

   Future<void> _initHive() async {
    try {
      // Initialize Hive box
      if (!Hive.isBoxOpen(outfitsBoxName)) {
        await Hive.openBox(outfitsBoxName);
      }
      outfitsBox = Hive.box(outfitsBoxName);

      // Check if we need to clear the cache
      await _checkMidnightReset();

      // Load data from cache or network
      await loadCachedOutfits();
    } catch (e) {
      print("Error initializing Hive for outfits: $e");
      // Fall back to network fetch if Hive initialization fails
      fetchOutfits();
    }
  }

  void _setupMidnightTimer() {
    // Cancel existing timer if it exists
    _midnightTimer?.cancel();
    
    // Schedule timer for next midnight
    DateTime now = DateTime.now();
    DateTime nextMidnight = DateTime(now.year, now.month, now.day + 1);
    Duration timeUntilMidnight = nextMidnight.difference(now);
    
    _midnightTimer = Timer(timeUntilMidnight, () {
      // Clear cache and fetch new data at midnight
      _clearCacheAndFetch();
      // Setup the timer for the next midnight
      _setupMidnightTimer();
    });
    
    print("Midnight timer scheduled. Next reset in ${timeUntilMidnight.inHours} hours, ${timeUntilMidnight.inMinutes % 60} minutes");
  }

  Future<void> _checkMidnightReset() async {
    try {
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      
      // Check if we've already cleared the cache today
      if (outfitsBox.containsKey(midnightClearKey)) {
        String lastClearDate = outfitsBox.get(midnightClearKey);
        
        if (lastClearDate != today) {
          // It's a new day, clear the cache
          await _clearCacheAndFetch();
          return;
        }
      } else {
        // First time running or key doesn't exist, set it
        await outfitsBox.put(midnightClearKey, today);
      }
    } catch (e) {
      print("Error checking midnight reset: $e");
    }
  }

   Future<void> _clearCacheAndFetch() async {
    try {
      // Clear outfit data
      if (outfitsBox.containsKey('outfits_data')) {
        await outfitsBox.delete('outfits_data');
      }
      
      // Clear last fetch time
      if (outfitsBox.containsKey(lastOutfitsFetchKey)) {
        await outfitsBox.delete(lastOutfitsFetchKey);
      }
      
      // Update the last clear date
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      await outfitsBox.put(midnightClearKey, today);
      
      print("Cache cleared at midnight. Fetching new outfits...");
      
      // Fetch new outfits
      await fetchOutfits();
    } catch (e) {
      print("Error clearing cache at midnight: $e");
    }
  }

   Future<void> loadCachedOutfits() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Check if we have cached data and if it's still valid
      if (outfitsBox.containsKey('outfits_data') && 
          outfitsBox.containsKey(lastOutfitsFetchKey)) {
        final lastFetch = DateTime.parse(outfitsBox.get(lastOutfitsFetchKey));
        final now = DateTime.now();

        // If cache is still valid, use it
        if (now.difference(lastFetch) < cacheValidDuration) {
          final cachedData = outfitsBox.get('outfits_data');
          List<dynamic> cachedOutfitsRaw = cachedData['outfits'] ?? [];
          
          List<Outfit> cachedOutfits = [];
          for (var outfitData in cachedOutfitsRaw) {
            try {
              Outfit outfit = Outfit(
                userId: outfitData['userId'] ?? '',
                outfitId: outfitData['outfitId'] ?? '',
                outfitName: outfitData['outfitName'] ?? '',
                apparelPointers: List<String>.from(outfitData['apparelPointers'] ?? []),
                dateCreated: outfitData['dateCreated'] ?? '',
                status: outfitData['status'] ?? '',
                tags: List<String>.from(outfitData['tags'] ?? []),
                description: outfitData['description'] ?? '',
              );
              
              // Restore apparels if they exist
              if (outfitData['apparels'] != null && outfitData['apparels'] is List) {
                for (var apparelData in outfitData['apparels']) {
                  Apparel apparel = Apparel(
                    apparelId: apparelData['apparelId'] ?? '',
                    apparelType: apparelData['apparelType'] ?? '',
                    apparelMediaUrl: apparelData['apparelMediaUrl'] ?? '',
                    productName: apparelData['productName'] ?? '',
                    brand: apparelData['brand'] ?? '',
                    colour: apparelData['colour'] ?? '',
                  );
                  outfit.apparels.add(apparel);
                }
              }
              
              cachedOutfits.add(outfit);
            } catch (e) {
              print("Error parsing cached outfit: $e");
            }
          }
          
          setState(() {
            outfits = cachedOutfits;
            isLoading = false;
          });
          
          print("Using cached outfits data");
          
          // For outfits that don't have apparel details, fetch them
          for (var outfit in cachedOutfits) {
            if (outfit.apparelPointers.isNotEmpty && outfit.apparels.isEmpty) {
              await fetchApparelDetails(outfit);
            }
          }
          
          // Optionally fetch latest data in background for next time
          _fetchAndCacheInBackground();
          return;
        }
      }

      // If no valid cache, fetch from network
      await fetchOutfits();
    } catch (e) {
      print("Error loading cached outfits: $e");
      setState(() {
        isLoading = false;
      });

      // Always fetch from network if there's an error with the cache
      await fetchOutfits();
    }
  }

    Future<void> _fetchAndCacheInBackground() async {
    // Fetch in background without loading indicators
    try {
      await fetchOutfits(showLoading: false);
    } catch (e) {
      print("Background fetch of outfits failed: $e");
    }
  }

  Future<void> _fetchUserGender() async {
    try {
      final userId = await SharedPrefsService.getUserId();
      if (userId == null) return;

      final authToken = await SharedPrefsService.getAuthToken();
      if (authToken == null) return;

      final response = await http.get(
        Uri.parse(
            '${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final userData = jsonResponse['data'];

        if (userData != null && userData['userGender'] != null) {
          setState(() {
            _userGender = userData['userGender'];
          });
        }
      }
    } catch (e) {
      print('Error fetching user gender: $e');
    }
  }

  Widget _buildEmptyOutfitCard() {
    final String imagePath;
    if (_userGender == null) {
      imagePath = 'assets/cardImages/women_recomm.png'; // Default image
    } else if (_userGender == 'MASCULINE') {
      imagePath = 'assets/cardImages/men_recomm.png';
    } else {
      imagePath = 'assets/cardImages/women_recomm.png';
    }

    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              imagePath, // Gender-based image
              height: 120,
              width: 120,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 24),
            const Text(
              'Fresh New Fits Everyday!',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              "That's it for today!",
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Come tomorrow to see some\nfresh new fits.',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> fetchOutfits({bool showLoading = true}) async {
    if (showLoading) {
      setState(() {
        isLoading = true;
      });
    }

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isLoading = false;
        });
        return;
      }

      // Get weather data to pass to the API
      final weatherData = await WeatherLocationService.getWeatherData();
      String weatherCondition = weatherData.condition;

      // Use the new API endpoint directly for all outfits
      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/get-today-outfits?userId=$userId&weather=$weatherCondition'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        List<Outfit> fetchedOutfits = [];
        if (jsonResponse['data'] != null) {
          Map<String, dynamic> outfitsByCategory = jsonResponse['data'];

          // Convert each category's outfit to an Outfit object
          outfitsByCategory.forEach((category, outfitData) {
            if (outfitData != null && outfitData is Map<String, dynamic>) {
              Outfit outfit = Outfit(
                userId: outfitData['userId'],
                outfitId: outfitData['outfitId'],
                outfitName: outfitData['outfitName'],
                apparelPointers:
                    List<String>.from(outfitData['apparelPointers']),
                dateCreated: outfitData['dateCreated'] ??
                    DateTime.now().toIso8601String(),
                status: outfitData['status'] ?? 'PRIMARY',
                tags: List<String>.from(outfitData['tags']),
                description: 'Category: ${category.toUpperCase()}',
              );
              fetchedOutfits.add(outfit);
            }
          });
        }

        // Create a list of Future calls for fetching apparel details in parallel
        List<Future<void>> apparelFutures = [];

        // Create all apparel fetch futures
        for (var outfit in fetchedOutfits) {
          if (outfit.apparelPointers.isNotEmpty) {
            apparelFutures.add(fetchApparelDetails(outfit));
          }
        }

        // Wait for all apparel details to be fetched in parallel
        await Future.wait(apparelFutures);

        // Prepare data for caching - serialize outfits
        List<Map<String, dynamic>> outfitsForCache = fetchedOutfits.map((outfit) {
          // Convert Outfit to Map
          Map<String, dynamic> outfitMap = {
            'userId': outfit.userId,
            'outfitId': outfit.outfitId,
            'outfitName': outfit.outfitName,
            'apparelPointers': outfit.apparelPointers,
            'dateCreated': outfit.dateCreated,
            'status': outfit.status,
            'tags': outfit.tags,
            'description': outfit.description,
            'apparels': outfit.apparels.map((apparel) => {
              'apparelId': apparel.apparelId,
              'apparelType': apparel.apparelType,
              'apparelMediaUrl': apparel.apparelMediaUrl,
              'productName': apparel.productName,
              'brand': apparel.brand,
              'colour': apparel.colour,
            }).toList(),
          };
          return outfitMap;
        }).toList();

        // Cache the data
        Map<String, dynamic> cacheData = {
          'outfits': outfitsForCache,
        };
        
        await outfitsBox.put('outfits_data', cacheData);
        await outfitsBox.put(lastOutfitsFetchKey, DateTime.now().toIso8601String());

        setState(() {
          outfits = fetchedOutfits;
          isLoading = false;
        });
      } else {
        print("Error fetching outfits: ${response.reasonPhrase}");
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching outfits: $e");
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<Outfit?> _fetchSingleOutfit(
      String userId, String authToken, String vibe) async {
    try {
      // Get weather data to pass to the API
      final weatherData = await WeatherLocationService.getWeatherData();
      String weatherCondition = weatherData.condition;

      // Use the new get-today-outfits endpoint
      final url = Uri.parse(
          '${ApiConstants.outfitApi}outfit/collections/get-today-outfits?userId=$userId&weather=$weatherCondition');

      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        if (jsonResponse['data'] != null) {
          print('Successfully fetched outfits for vibe: $vibe');

          // The API now returns multiple outfits by category
          Map<String, dynamic> outfitsByCategory = jsonResponse['data'];

          // Try to get the outfit matching the requested vibe
          // Convert vibe to lowercase to match the category key
          String vibeLower = vibe.toLowerCase();

          // Check if the exact vibe exists in the response
          if (outfitsByCategory.containsKey(vibeLower)) {
            var outfitData = outfitsByCategory[vibeLower];
            return Outfit(
              userId: outfitData['userId'],
              outfitId: outfitData['outfitId'],
              outfitName: outfitData['outfitName'],
              apparelPointers:
                  List<String>.from(outfitData['apparelPointers']),
              tags: List<String>.from(outfitData['tags']),
              dateCreated:
                  outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
              status: outfitData['status'] ?? 'PRIMARY',
              description: 'Category: ${vibeLower.toUpperCase()}',
            );
          }
          // Check if the vibe exists with first letter capitalized
          else if (outfitsByCategory.containsKey(vibe)) {
            var outfitData = outfitsByCategory[vibe];
            return Outfit(
              userId: outfitData['userId'],
              outfitId: outfitData['outfitId'],
              outfitName: outfitData['outfitName'],
              apparelPointers:
                  List<String>.from(outfitData['apparelPointers']),
              tags: List<String>.from(outfitData['tags']),
              dateCreated:
                  outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
              status: outfitData['status'] ?? 'PRIMARY',
              description: 'Category: ${vibe.toUpperCase()}',
            );
          }
          // If no match, try to return any available outfit
          else if (outfitsByCategory.isNotEmpty) {
            String firstCategory = outfitsByCategory.keys.first;
            var outfitData = outfitsByCategory[firstCategory];
            return Outfit(
              userId: outfitData['userId'],
              outfitId: outfitData['outfitId'],
              outfitName: outfitData['outfitName'],
              apparelPointers:
                  List<String>.from(outfitData['apparelPointers']),
              tags: List<String>.from(outfitData['tags']),
              dateCreated:
                  outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
              status: outfitData['status'] ?? 'PRIMARY',
              description: 'Category: ${firstCategory.toUpperCase()}',
            );
          }
        }
      } else {
        print("Error fetching outfit for vibe $vibe: ${response.statusCode}");
      }
      return null;
    } catch (e) {
      print("Exception in _fetchSingleOutfit for vibe $vibe: $e");
      return null;
    }
  }

  Future<void> fetchCollections() async {
    setState(() {
      isCollectionLoading = true;
    });

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isCollectionLoading = false;
        });
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/list?userId=$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        print("Collections response: $responseBody"); // For debugging

        List<OutfitCollection> fetchedCollections = [];
        if (jsonResponse['data'] != null) {
          for (var collectionJson in jsonResponse['data']) {
            OutfitCollection collection =
                OutfitCollection.fromJson(collectionJson);
            fetchedCollections.add(collection);
          }
        }

        setState(() {
          collections = fetchedCollections;
          isCollectionLoading = false;
        });
      } else {
        print("Error fetching collections: ${response.reasonPhrase}");
        setState(() {
          isCollectionLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching collections: $e");
      setState(() {
        isCollectionLoading = false;
      });
    }
  }

  Future<void> createCollection(String collectionName, [Outfit? outfit]) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'POST',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections?userId=$userId'));

      // Create the request body based on whether an outfit is provided
      var requestBody = {
        "outfitCollectionName": collectionName,
        "outfitPointers": outfit != null ? [outfit.outfitId] : []
      };

      request.body = jsonEncode(requestBody);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 201) {
        String responseBody = await response.stream.bytesToString();
        print("Collection created successfully: $responseBody");

        // Refresh collections list
        await fetchCollections();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Collection created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error creating collection: ${response.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print("Exception when creating collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> saveOutfitToCollection(
      OutfitCollection collection, Outfit outfit) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      // Check if outfit is already in the collection
      if (collection.outfitPointers.contains(outfit.outfitId)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Outfit already in ${collection.outfitCollectionName}'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context);
        return;
      }

      // Update collection with new outfit
      var collectionHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      // Create a new list with the current outfit added
      List<String> updatedOutfitPointers = [
        ...collection.outfitPointers,
        outfit.outfitId
      ];

      var collectionRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/${collection.outfitCollectionId}?userId=$userId'));

      collectionRequest.body = jsonEncode({
        "outfitCollectionName": collection.outfitCollectionName,
        "outfitPointers": updatedOutfitPointers
      });

      collectionRequest.headers.addAll(collectionHeaders);

      http.StreamedResponse collectionResponse = await collectionRequest.send();

      // Update outfit status
      var outfitHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var outfitRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/outfits/${outfit.outfitId}?userId=$userId'));

      outfitRequest.body = jsonEncode({
        "status": "WISHLIST",
        "outfitCollectionId": collection.outfitCollectionId
      });

      outfitRequest.headers.addAll(outfitHeaders);

      http.StreamedResponse outfitResponse = await outfitRequest.send();

      if (collectionResponse.statusCode == 200 &&
          outfitResponse.statusCode == 200) {
        String collectionResponseBody =
            await collectionResponse.stream.bytesToString();
        String outfitResponseBody = await outfitResponse.stream.bytesToString();
        print("Collection updated successfully: $collectionResponseBody");
        print("Outfit updated successfully: $outfitResponseBody");

        // Refresh collections list
        await fetchCollections();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Outfit saved to ${collection.outfitCollectionName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error updating collection: ${collectionResponse.reasonPhrase}");
        print("Error updating outfit: ${outfitResponse.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save outfit to collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
      Navigator.pop(context);
    } catch (e) {
      print("Exception when saving outfit to collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
      Navigator.pop(context);
    }
  }

  Future<void> fetchApparelDetails(Outfit outfit) async {
  try {
    String? authToken = await SharedPrefsService.getAuthToken();
    String? userId = await SharedPrefsService.getUserId();

    if (authToken == null || userId == null) {
      print("Auth token or userId is null");
      return;
    }

    // For better performance, only fetch the first four apparels
    for (int i = 0; i < outfit.apparelPointers.length && i < 4; i++) {
      if (outfit.apparelPointers[i].isEmpty) {
        continue;
      }

      var headers = {'Authorization': 'Bearer $authToken'};

      // Modified URL to include userId as query parameter
      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.apparelApi}apparel/${outfit.apparelPointers[i]}?userId=$userId'));
      request.headers.addAll(headers);

      try {
        http.StreamedResponse response = await request.send();

        if (response.statusCode == 200) {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

          if (jsonResponse['data'] != null) {
            Apparel apparel = Apparel.fromJson(jsonResponse);
            outfit.apparels.add(apparel);
          }
        } else {
          print("Info: Apparel ${outfit.apparelPointers[i]} not found or not accessible: ${response.reasonPhrase}");
        }
      } catch (e) {
        print("Info: Exception fetching apparel ${outfit.apparelPointers[i]}: $e");
        // Continue to next apparel even if one fails
      }
    }
  } catch (e) {
    print("Exception when fetching apparel details: $e");
  }
}

  // Current index to track which card to show
  int currentCardIndex = 0;

  // Widget to show when no cards are available
  Widget _buildNoCardsWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        child: Container(
          height: 502,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/emptyImages/almirah.png', // Make sure this matches your asset path
                width: 300,
                height: 300,
              ),
              const SizedBox(height: 16),
              const Text(
                "Your wardrobe is empty!",
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  "Add 6 pieces to start—2 tops, 2 bottoms & 2 shoes.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStyleJournalCard(OutfitCollection collection) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.28,
      height: MediaQuery.of(context).size.width * 0.28,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              _getGradientColorsForCollection(collection.outfitCollectionName),
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // Image circle for collection image
          Positioned(
            top: 12,
            right: 12,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: collection.outfitCollectionMediaUrl.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: Image.network(
                          collection.outfitCollectionMediaUrl,
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            _getIconForCollection(
                                collection.outfitCollectionName),
                            size: 24,
                            color: Colors.black,
                          ),
                        ),
                      )
                    : Icon(
                        _getIconForCollection(collection.outfitCollectionName),
                        size: 24,
                        color: Colors.black,
                      ),
              ),
            ),
          ),
          // Title text
          Positioned(
            bottom: 16,
            left: 16,
            child: Text(
              collection.outfitCollectionName,
              style: const TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 14,
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get gradient colors based on collection name
  List<Color> _getGradientColorsForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return [const Color(0xFFFFF8E7), const Color(0xFFFFE5C4)];
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return [const Color(0xFFFFF1F1), const Color(0xFFFFD6D6)];
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return [const Color(0xFFF5F1FF), const Color(0xFFE8E0FF)];
    }
    // Default gradient
    return [const Color(0xFFE3F2FD), const Color(0xFFBBDEFB)];
  }

  void _showSaveOutfitBottomSheet(BuildContext context, [Outfit? outfit]) {
    // Store the current outfit to use in API calls
    currentOutfit = outfit ??
        (currentCardIndex < outfits.length ? outfits[currentCardIndex] : null);

    // If no outfit is available, show an error
    if (currentOutfit == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No outfit to save'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Fetch collections before showing the bottom sheet
    fetchCollections().then((_) {
      showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Save to',
                        style: TextStyle(
                          fontFamily: 'SatoshiM',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 24),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: isCollectionLoading
                        ? const SizedBox()
                        : SingleChildScrollView(
                            child: Column(
                              children: [
                                _buildSaveOptionRow(
                                  title: 'New Style Group',
                                  outfitCount: 0,
                                  isNew: true,
                                  onTap: () {
                                    _showNewStyleGroupModal(context);
                                  },
                                ),
                                if (collections.isNotEmpty)
                                  ...collections.map((collection) {
                                    return _buildSaveOptionRow(
                                      title: collection.outfitCollectionName,
                                      outfitCount:
                                          collection.outfitPointers.length,
                                      collection: collection,
                                      onTap: () {
                                        saveOutfitToCollection(
                                            collection, currentOutfit!);
                                      },
                                    );
                                  }).toList(),
                              ],
                            ),
                          ),
                  )
                ],
              ),
            );
          });
        },
      );
    });
  }

  IconData _getIconForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return Icons.work_outline;
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return Icons.favorite;
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return Icons.wb_sunny_outlined;
    }
    return Icons.style;
  }

// Helper method to get color based on collection name
  Color _getColorForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return const Color(0xFFFFF8E7);
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return const Color(0xFFFFF1F1);
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return const Color(0xFFF5F1FF);
    }
    return Colors.white;
  }

  void _showNewStyleGroupModal(BuildContext context) {
    // Create a local controller that won't be disposed with the screen
    TextEditingController localController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'New Style Group',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 24),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  height: 200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.help_outline,
                            color: Colors.grey,
                            size: 50,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'The image is auto generated\nbased on the name',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Journal Name',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: localController,
                  decoration: InputDecoration(
                    hintText: 'Party at Night, Fun day out...',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (localController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Please enter a name for your style group'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // Store the value before closing modal
                      final collectionName = localController.text.trim();

                      // Close this modal
                      Navigator.pop(context);

                      // Create collection WITHOUT requiring an outfit
                      createCollection(collectionName);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xffF76037),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSaveOptionRow({
    required String title,
    required int outfitCount,
    required VoidCallback onTap,
    bool isNew = false,
    OutfitCollection? collection,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 83,
            height: 83,
            decoration: BoxDecoration(
              color: isNew ? Colors.white : _getColorForCollection(title),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: isNew
                  ? const Icon(Icons.add, color: Colors.black, size: 24)
                  : (collection != null &&
                          collection.outfitCollectionMediaUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Image.network(
                            collection.outfitCollectionMediaUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              _getIconForCollection(title),
                              size: 24,
                              color: Colors.black,
                            ),
                          ),
                        )
                      : Icon(_getIconForCollection(title),
                          size: 24, color: Colors.black)),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$outfitCount outfits',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            backgroundColor: Colors.grey[200],
            child: IconButton(
              icon: const Icon(Icons.add, color: Colors.grey),
              onPressed: onTap,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      isLoading: false,
      gifAsset: 'assets/loader/loading.json',
      child: Container(
        color: const Color(0xffF8F7F7),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.all(18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Today’s Fits",
                      style: TextStyle(
                        fontFamily: "SatoshiM",
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'Styled just for you—your vibe, your way!',
                      style: TextStyle(
                        fontFamily: "SatoshiR",
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 480,
                width: double.infinity,
                child: isLoading
                    ? Center(
                        child: _buildShimmerOutfitCard(),
                      )
                    : outfits.isEmpty
                        ? _buildNoCardsWidget()
                        : Column(
                            children: [
                              Expanded(
                                child: PageView.builder(
                                  controller:
                                      PageController(viewportFraction: 0.85),
                                  onPageChanged: (index) {
                                    setState(() {
                                      currentCardIndex = index;
                                    });
                                  },
                                  itemCount: outfits.length + 1,
                                  itemBuilder: (context, index) {
                                    if (index < outfits.length) {
                                      final Outfit outfitData = outfits[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10.0),
                                        child: Container(
                                          margin: const EdgeInsets.symmetric(
                                              vertical: 10),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.05),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: GestureDetector(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      NewOfficeWearDetailsScreen(
                                                          outfit: outfitData),
                                                ),
                                              );
                                            },
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(16.0),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Title
                                                  DefaultTextStyle(
                                                    style: const TextStyle(
                                                      fontFamily: "SatoshiR",
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      decoration:
                                                          TextDecoration.none,
                                                      color: Colors.black,
                                                    ),
                                                    child: Text(
                                                        outfitData.outfitName),
                                                  ),
                                                  const SizedBox(height: 16),

                                                  // Image grid - reuse your existing image grid logic
                                                  _buildOutfitImagesGrid(
                                                      outfitData),

                                                  const SizedBox(height: 16),

                                                  // Action buttons
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        FeatherIcons.thumbsUp,
                                                        size: 20,
                                                        color:
                                                            Color(0xff8F8F8F),
                                                      ),
                                                      const SizedBox(width: 24),
                                                      Icon(FeatherIcons.thumbsDown,
                                                          color:
                                                              Color(0xff8F8F8F),
                                                          size: 20),
                                                      const SizedBox(width: 24),
                                                      Icon(FeatherIcons.send,
                                                          color:
                                                              Color(0xff8F8F8F),
                                                          size: 20),
                                                      const Spacer(),
                                                      GestureDetector(
                                                        onTap: () {
                                                          _showSaveOutfitBottomSheet(
                                                              context,
                                                              outfitData);
                                                        },
                                                        child: Icon(
                                                            FeatherIcons
                                                                .bookmark,
                                                            color: Color(
                                                                0xff8F8F8F),
                                                            size: 20),
                                                      ),
                                                    ],
                                                  ),

                                                  const SizedBox(height: 12),

                                                  // Description
                                                  DefaultTextStyle(
                                                    style: TextStyle(
                                                      fontFamily: "SatoshiR",
                                                      fontSize: 14,
                                                      color: Colors.grey[700],
                                                      decoration:
                                                          TextDecoration.none,
                                                    ),
                                                    child: Text(outfitData
                                                            .description.isEmpty
                                                        ? description[index %
                                                            description.length]
                                                        : outfitData
                                                            .description),
                                                  ),

                                                  const SizedBox(height: 12),

                                                  // Tags
                                                  Wrap(
                                                    spacing: 8,
                                                    runSpacing: 8,
                                                    children:
                                                        _buildColorfulTags(
                                                            outfitData.tags),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    } else {
                                      return _buildEmptyOutfitCard();
                                    }
                                  },
                                ),
                              ),
                              // Pagination indicator dots
                              // Padding(
                              //   padding: const EdgeInsets.only(bottom: 10.0),
                              //   child: Row(
                              //     mainAxisAlignment: MainAxisAlignment.center,
                              //     children: List.generate(
                              //       outfits.length,
                              //       (index) => Container(
                              //         margin: const EdgeInsets.symmetric(
                              //             horizontal: 3),
                              //         width: 8,
                              //         height: 8,
                              //         decoration: BoxDecoration(
                              //           shape: BoxShape.circle,
                              //           color: currentCardIndex == index
                              //               ? Color(0xffF76037)
                              //               : Colors.grey[300],
                              //         ),
                              //       ),
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
              ),
              if (currentCardIndex < outfits.length)
                Center(
                  child: Text(
                    "${currentCardIndex + 1} of ${outfits.length}",
                    style: TextStyle(fontFamily: "SatoshiR"),
                  ),
                ),
              const Padding(
                padding: EdgeInsets.fromLTRB(18, 24, 18, 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Style Journal',
                      style: TextStyle(
                        fontFamily: "SatoshiM",
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'All your liked outfits, at your fingertips.',
                      style: TextStyle(
                        fontFamily: "SatoshiR",
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: isCollectionLoading
                    ? const SizedBox()
                    : Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: [
                          // Always show "New Collection" card first
                          GestureDetector(
                            onTap: () {
                              _showNewStyleGroupModal(context);
                            },
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.28,
                              height: MediaQuery.of(context).size.width * 0.28,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFFFF0EE),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      color: Color(0xffF76037),
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    "New Collection",
                                    style: TextStyle(
                                      fontFamily: "SatoshiR",
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Show all existing collections
                          ...collections.map((collection) {
                            return GestureDetector(
                              onTap: () {
                                // Navigate to collection details with the collection data
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        CollectionDetailsScreen(
                                            collection: collection),
                                  ),
                                );
                              },
                              child: _buildStyleJournalCard(collection),
                            );
                          }).toList(),
                        ],
                      ),
              ),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerOutfitCard() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: LinearProgressIndicator(
              value: null, // Indeterminate progress
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF76037)),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icons/star.png', // Using star from assets
                  width: 14,
                  height: 14,
                ),
                const SizedBox(width: 8),
                Text(
                  'Understanding your style...',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOutfitImagesGrid(Outfit outfitData) {
    if (outfitData.apparels.isEmpty) {
      return Container(
        height: 245,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(Icons.broken_image),
      );
    } else if (outfitData.apparels.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          outfitData.apparels[0].apparelMediaUrl,
          height: 245,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Container(
            height: 245,
            color: Colors.grey[300],
            child: const Icon(Icons.broken_image),
          ),
        ),
      );
    } else if (outfitData.apparels.length == 2) {
      return Column(
        children: [
          // Top image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfitData.apparels[0].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          // Bottom image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfitData.apparels[1].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
        ],
      );
    } else if (outfitData.apparels.length == 3) {
      return Column(
        children: [
          // Top image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              outfitData.apparels[0].apparelMediaUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 120,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          // Bottom row of images with divider
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[1].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[2].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      return Column(
        children: [
          // Top row of images with divider
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[0].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[1].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Horizontal divider
          Container(
            height: 1,
            color: const Color(0xFFE0E0E0),
            margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          // Bottom row of images with divider
          Row(
            children: [
              // Left image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[2].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
              // Vertical divider
              Container(
                height: 120,
                width: 1,
                color: const Color(0xFFE0E0E0),
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              // Right image
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfitData.apparels[3].apparelMediaUrl,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  // Helper function to build colorful tags
  List<Widget> _buildColorfulTags(List<String> tags) {
    // Map of tag colors
    final Map<String, Color> tagColors = {
      'Casual': const Color(0xFFFCE4EC),
      'Night Out': const Color(0xFFFFF8E1),
      'Date Night': const Color(0xFFF1F8E9),
    };

    return tags.map((tag) {
      // Use the predefined color or default to a light gray
      final Color tagColor = tagColors[tag] ?? const Color(0xFFEEEEEE);

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tagColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          tag,
          style: const TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 12,
            color: Colors.black87,
          ),
        ),
      );
    }).toList();
  }
}
