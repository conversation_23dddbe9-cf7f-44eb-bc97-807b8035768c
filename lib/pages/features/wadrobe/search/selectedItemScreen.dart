// SELECTED ITEM SCREEN
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/editItemScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/searchItemScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/successScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';

class SelectedItemScreen extends StatefulWidget {
  final List<SearchResult> selectedItems;

  const SelectedItemScreen({Key? key, required this.selectedItems})
      : super(key: key);

  @override
  State<SelectedItemScreen> createState() => _SelectedItemScreenState();
}

class _SelectedItemScreenState extends State<SelectedItemScreen> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _saveApparels() async {
    final userId = await SharedPrefsService.getUserId();
    final authToken = await SharedPrefsService.getAuthToken();

    if (userId == null || authToken == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Authentication error. Please log in again.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // First navigate to success screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            SuccessScreen(selectedItems: widget.selectedItems),
      ),
    );

    try {
      final uri = Uri.parse('${ApiConstants.apparelApi}apparel');

      // Save each item individually
      for (final item in widget.selectedItems) {
        // Extract data from search results JSON
        final Map<String, dynamic> apparelMetaData = {
          "apparelProfile": item.taggedData?['apparelProfile'],
          "apparelCategory": item.taggedData?['apparelCategory'],
          "apparelMediaUrl": item.imageUrl,
          "pattern": item.taggedData?['pattern'],
          "colour": item.primaryColor,
          "fit": item.taggedData?['fit'],
          "length": item.taggedData?['length'],
          "fabric": item.taggedData?['fabric'],
          "brand": item.brand,
          "productId": item.id,
          "productName": item.title,
          "productUrl": item.taggedData?['productUrl'],
          "category": item.taggedData?['category'],
          "transparency": item.taggedData?['transparency'],
          "waistRise": item.taggedData?['waistRise'],
          "occasion": item.taggedData?['occasion'],
          "weave": item.taggedData?['weave'],
          "clothingType": item.taggedData?['clothingType'],
        };

        // Remove null values from apparelMetaData
        apparelMetaData.removeWhere((key, value) => value == null);

        final response = await http.post(
          uri,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken'
          },
          body: jsonEncode({
            "source": "USER",
            "sourceId": userId,
            "apparelMetaData": apparelMetaData
          }),
        );

        if (response.statusCode != 201) {
          print(
              'Failed to save apparel: ${response.statusCode} - ${response.body}');
          throw Exception('Failed to save apparel');
        }
      }
    } catch (e) {
      print('Error details: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving items: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildTag(String text, double screenWidth) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.03,
        vertical: screenWidth * 0.015,
      ),
      margin: EdgeInsets.only(right: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: screenWidth * 0.035,
          fontFamily: "SatoshiR",
        ),
      ),
    );
  }

  List<Widget> _buildScrapedDataTags(SearchResult item) {
    final apparelDetails = item.apparelDetails;
    final taggedData = item.taggedData;
    final List<Widget> tags = [];
    final screenWidth = MediaQuery.of(context).size.width;

    // Add brand from product details
    if (item.brand.isNotEmpty) {
      tags.add(_buildTag(item.brand, screenWidth));
    }

    // Add color
    if (item.primaryColor.isNotEmpty) {
      tags.add(_buildTag(item.primaryColor, screenWidth));
    }

    // Add price if available
    if (item.price.isNotEmpty) {
      tags.add(_buildTag('Price: ${item.price}', screenWidth));
    }

    if (apparelDetails != null) {
      // Add brand name if available
      if (apparelDetails['brand_name'] != null) {
        tags.add(_buildTag(apparelDetails['brand_name'], screenWidth));
      }

      // Add color if available
      if (apparelDetails['apparel_color'] != null) {
        tags.add(_buildTag(apparelDetails['apparel_color'], screenWidth));
      }

      // Add attributes
      if (apparelDetails['apparel_attributes'] != null) {
        for (String attribute
            in (apparelDetails['apparel_attributes'] as List)) {
          tags.add(_buildTag(attribute, screenWidth));
        }
      }
    }

    if (taggedData != null) {
      // Add tagged attributes
      final relevantFields = [
        'apparelType',
        'colour',
        'fit',
        'length',
        'fabric',
        'transparency'
      ];
      for (var field in relevantFields) {
        if (taggedData[field] != null) {
          tags.add(_buildTag(
              taggedData[field].toString().replaceAll('_', ' '), screenWidth));
        }
      }
    }

    return tags;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = MediaQuery.of(context).padding;
    final availableHeight = screenHeight - padding.top - padding.bottom;
    final currentItem = widget.selectedItems[_currentIndex];

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SizedBox(
          height: availableHeight,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: availableHeight * 0.02),
              Padding(
                padding: EdgeInsets.fromLTRB(18, 0, 16, availableHeight * 0.01),
                child: Text(
                  widget.selectedItems.length > 1
                      ? 'Selected Items'
                      : 'Selected Item',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: screenWidth * 0.055,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: Text(
                  "We've analyzed your item(s). Here are the details we found.",
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    color: Colors.black,
                    fontSize: 12,
                  ),
                ),
              ),
              SizedBox(height: availableHeight * 0.02),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 18),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE7E7E7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: widget.selectedItems.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      final item = widget.selectedItems[index];
                      return Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    item.title,
                                    style: TextStyle(
                                      fontFamily: "SatoshiR",
                                      fontSize: screenWidth * 0.04,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => EditItemScreen(
                                          selectedItem: item,
                                        ),
                                      ),
                                    );
                                  },
                                  child: CircleAvatar(
                                    radius: screenWidth * 0.04,
                                    backgroundColor: Colors.white,
                                    child: Icon(Icons.edit,
                                        size: screenWidth * 0.045,
                                        color: Colors.black),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Image.network(
                              item.imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                color: Colors.grey[200],
                                child: const Icon(Icons.image_not_supported,
                                    color: Colors.grey),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Color(0xffF8F7F7),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12),
                                ),
                              ),
                              width: double.infinity,
                              child: Padding(
                                padding: EdgeInsets.all(screenWidth * 0.04),
                                child: SingleChildScrollView(
                                  child: Wrap(
                                    spacing: screenWidth * 0.02,
                                    runSpacing: screenWidth * 0.02,
                                    children: _buildScrapedDataTags(item),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Center(
                child: Padding(
                  padding: EdgeInsets.all(screenWidth * 0.04),
                  child: Text(
                    '${_currentIndex + 1} of ${widget.selectedItems.length}',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: screenWidth * 0.035,
                      fontFamily: "SatoshiR",
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04,
                  vertical: availableHeight * 0.02,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: availableHeight * 0.06,
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Color(0xFFF76037)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: Text(
                            'Back',
                            style: TextStyle(
                              color: const Color(0xFFF76037),
                              fontSize: screenWidth * 0.035,
                              fontFamily: "SatoshiR",
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: screenWidth * 0.04),
                    Expanded(
                      child: SizedBox(
                        height: availableHeight * 0.06,
                        child: ElevatedButton(
                          onPressed: _saveApparels,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFF76037),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Done',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.035,
                              fontFamily: "SatoshiR",
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
