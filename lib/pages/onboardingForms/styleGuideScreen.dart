import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/authScreens/signUp/signUpScreen.dart';
import 'package:monova_ai_stylist/pages/onboardingScreens/onboardingScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/widgets/bottomNavBar/bottomNavBar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class StyleGuideResultScreen extends StatelessWidget {
  final Map<String, dynamic> styleGuideData;
  final VoidCallback onFinished;

  const StyleGuideResultScreen({
    Key? key,
    required this.styleGuideData,
    required this.onFinished,
  }) : super(key: key);

  Future<void> _saveStyleProfile() async {
    try {
      // Get auth data from SharedPrefs
      final authData = await SharedPrefsService.getAuthData();
      final authToken = authData['authToken'];
      final userId = authData['userId'];

      // If not logged in, navigate to signup
      if (authToken == null || userId == null) {
        return;
      }

      // Get user data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('userData');
      Map<String, dynamic> userDataMap = {};

      if (userData != null) {
        userDataMap = jsonDecode(userData);
      }

      // Ensure we're using the cloud URL from Azure, not the local path
      String profileImageUrl = await SharedPrefsService.getProfilePhoto() ?? "";

      // Validate the URL is from Azure blob storage
      if (profileImageUrl.isEmpty ||
          !profileImageUrl
              .startsWith('https://monovaoutfits.blob.core.windows.net')) {
        print('WARNING: Invalid profile image URL: $profileImageUrl');
        profileImageUrl = "";
      } else {
        print('Using valid Azure URL in style profile: $profileImageUrl');
      }

      // Create request body by merging user data and style guide data
      final requestBody = {
        "userId": userId,
        "userAge": userDataMap["userAge"],
        "userGender": userDataMap["userGender"],
        "userUndertone": userDataMap["userUndertone"],
        "userSkinTone": userDataMap["userSkinTone"],
        "userBodyType": userDataMap["userBodyType"],
        "userHeight": userDataMap["userHeight"],
        "userHeightMetric": userDataMap["userHeightMetric"],
        "userEyeColor": userDataMap["userEyeColor"],
        "userHairColor": userDataMap["userHairColor"],
        "userContrast": userDataMap["userContrast"],
        "userSeason": userDataMap["userSeason"],
        "userProfileImageUrl": profileImageUrl,
        "styleGuide": styleGuideData['data'],
      };

      // Make API call to save style profile
      final response = await http.post(
        Uri.parse('${ApiConstants.styleProfileApi}style-profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 201) {
        print('Style profile saved successfully: ${response.body}');
      } else {
        print('Failed to save style profile: ${response.body}');
      }
    } catch (e) {
      print('Error saving style profile: $e');
    }
  }

  // Modified method to handle save button click
  void _handleSaveGuide(BuildContext context) async {
    try {
      // Check if user is logged in
      final isLoggedIn = await SharedPrefsService.isLoggedIn();

      if (isLoggedIn) {
        // If logged in, save style profile and navigate to bottom nav
        await _saveStyleProfile();

        // Navigate to BottomNavScreen with index 1 (Wardrobe)
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (context) => const BottomNavScreen(initialIndex: 1)),
          (route) => false,
        );
      } else {
        // If not logged in, get name and navigate to signup
        final name = await _getUserName();

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SignUpScreen(
              initialName: name,
              styleGuideData: styleGuideData,
            ),
          ),
        );
      }
    } catch (e) {
      print('Error in _handleSaveGuide: $e');
      // Show error to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Something went wrong. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<String> _getUserName() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString('userData');
    if (userData != null) {
      final Map<String, dynamic> userDataMap =
          Map<String, dynamic>.from(jsonDecode(userData) as Map);
      return userDataMap['name'] ?? '';
    }
    return '';
  }

  void _navigateToSignup(BuildContext context) async {
    final name = await _getUserName();

    // Use pushReplacement to prevent going back to this screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => SignUpScreen(
          initialName: name,
          styleGuideData: styleGuideData,
        ),
      ),
    );
  }

  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userData');
    print('User data cleared');
  }

  @override
  Widget build(BuildContext context) {
    // Extract data from styleGuideData with null safety
    final data = styleGuideData['data'] ?? {};
    final seasonOutput = data['SeasonOutput'] ?? {};
    final heightOutput = data['HeightOutput'] ?? {};
    final bodyOutput = data['BodyOutput'] ?? {};

    return WillPopScope(
      // Handle back button press
      onWillPop: () async {
        await _clearUserData(); // Clear data when back button is pressed
        onFinished();
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => OnboardingScreen()),
        );
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          scrolledUnderElevation: 0,
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () async {
              await _clearUserData();
              onFinished();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => OnboardingScreen()),
              );
            },
          ),
          title: const Text(
            'Style Guide',
            style: TextStyle(
              fontFamily: 'SatoshiM',
              color: Colors.black,
              fontSize: 18,
            ),
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Section
                    const Text(
                      '✨ Welcome to your style guide!',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Your style guide helps you quickly identify what suits you best, making outfits decisions effortless.',
                      style: TextStyle(
                        fontFamily: 'SatoshiR',
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Divider(color: Colors.grey[300]),
                    const SizedBox(height: 16),

                    // Undertone and Color Season Section - with null checks
                    if (seasonOutput['SeasonSuggestions'] != null &&
                        seasonOutput['SeasonSuggestions'] is List &&
                        (seasonOutput['SeasonSuggestions'] as List).isNotEmpty)
                      Text(
                        (seasonOutput['SeasonSuggestions'] as List)[0]
                            .toString(),
                        style: const TextStyle(
                          fontFamily: 'SatoshiR',
                          fontSize: 14,
                        ),
                      ),
                    const SizedBox(height: 8),
                    if (seasonOutput['SeasonSuggestions'] != null &&
                        seasonOutput['SeasonSuggestions'] is List &&
                        (seasonOutput['SeasonSuggestions'] as List).length > 1)
                      Text(
                        (seasonOutput['SeasonSuggestions'] as List)[1]
                            .toString(),
                        style: const TextStyle(
                          fontFamily: 'SatoshiR',
                          fontSize: 14,
                        ),
                      ),
                    const SizedBox(height: 16),

                    // Colors to Wear Section
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          const Text(
                            'Colours to wear!',
                            style: TextStyle(
                              fontFamily: 'SatoshiM',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (seasonOutput['SeasonTags'] != null &&
                              seasonOutput['SeasonTags'] is List)
                            Text(
                              (seasonOutput['SeasonTags'] as List)
                                  .map((tag) => tag.toString())
                                  .join(', '),
                              style: const TextStyle(
                                fontFamily: 'SatoshiR',
                                fontSize: 14,
                              ),
                            ),
                          const SizedBox(height: 12),

                          // Color Palette with null check
                          if (seasonOutput['ColorPaletteUrl'] != null)
                            Image.network(
                              seasonOutput['ColorPaletteUrl'].toString(),
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset("assets/staticImages/color.png");
                              },
                            ),
                          const SizedBox(height: 8),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),
                    Divider(color: Colors.grey[300]),
                    const SizedBox(height: 16),

                    // Body Shape Section with null check
                    if (bodyOutput['BodyTypeSuggestions'] != null)
                      Text(
                        bodyOutput['BodyTypeSuggestions'].toString(),
                        style: const TextStyle(
                          fontFamily: 'SatoshiR',
                          fontSize: 14,
                        ),
                      ),
                    const SizedBox(height: 16),

                    // Height advice with null check
                    if (heightOutput['HeightSuggestions'] != null)
                      Text(
                        heightOutput['HeightSuggestions'].toString(),
                        style: const TextStyle(
                          fontFamily: 'SatoshiR',
                          fontSize: 14,
                        ),
                      ),
                    const SizedBox(height: 16),

                    // Fit Tags
                    if (bodyOutput['BodyTypeTags'] != null &&
                        bodyOutput['BodyTypeTags'] is Map &&
                        bodyOutput['BodyTypeTags']['FitTags'] != null &&
                        bodyOutput['BodyTypeTags']['FitTags'] is List)
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          for (var tag in bodyOutput['BodyTypeTags']['FitTags'])
                            _buildTag(tag.toString()),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // Pattern Tags
                    if (bodyOutput['BodyTypeTags'] != null &&
                        bodyOutput['BodyTypeTags'] is Map &&
                        bodyOutput['BodyTypeTags']['PatternTags'] != null &&
                        bodyOutput['BodyTypeTags']['PatternTags'] is List)
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          for (var tag in bodyOutput['BodyTypeTags']
                              ['PatternTags'])
                            _buildTag(tag.toString()),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // Height Tags
                    if (heightOutput['HeightTags'] != null &&
                        heightOutput['HeightTags'] is List)
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          for (var tag in heightOutput['HeightTags'])
                            _buildTag(tag.toString()),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // Outfit Images
                    if (data['StyleGuideMediaUrls'] != null &&
                        data['StyleGuideMediaUrls'] is List &&
                        (data['StyleGuideMediaUrls'] as List).isNotEmpty)
                      Center(
                        child: Wrap(
                          spacing: 40,
                          runSpacing: 12,
                          children:
                              _buildOutfitImages(data['StyleGuideMediaUrls']),
                        ),
                      )
                    else
                      Center(
                        child: Wrap(
                          spacing: 40,
                          runSpacing: 12,
                          children: [
                            _buildOutfitImage('assets/staticImages/cloth.png'),
                            _buildOutfitImage('assets/staticImages/cloth.png'),
                            _buildOutfitImage('assets/staticImages/cloth.png'),
                            _buildOutfitImage('assets/staticImages/cloth.png'),
                          ],
                        ),
                      ),
                    const SizedBox(height: 16),
                    Divider(color: Colors.grey[300]),
                    const SizedBox(height: 16),

                    // Styling Tip
                    const Text(
                      '✨ Keep it stylish and sharp!',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'Choose small to medium accessories, subtle prints, and well-fitted bottoms to enhance your frame.',
                      style: TextStyle(
                        fontFamily: 'SatoshiR',
                        fontSize: 14,
                      ),
                    ),
                    // Add extra padding at the bottom for the floating button
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
            // Floating save button
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () => _handleSaveGuide(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFF76037),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Save My Guide',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build outfit images from nested or flat arrays
  List<Widget> _buildOutfitImages(List mediaUrls) {
    List<Widget> widgets = [];

    for (var item in mediaUrls) {
      if (item is List) {
        // If it's a nested list, add each URL in the list
        for (var url in item) {
          if (url is String) {
            widgets.add(_buildOutfitNetworkImage(url));
          }
        }
      } else if (item is String) {
        // If it's a direct string URL
        widgets.add(_buildOutfitNetworkImage(item));
      }
    }

    return widgets;
  }

  Widget _buildTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFFFEECE7),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Color(0xFFF76037),
          fontFamily: 'SatoshiR',
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildOutfitImage(String imagePath) {
    return Container(
      width: 150,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
        image: DecorationImage(
          image: AssetImage(imagePath),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildOutfitNetworkImage(String imageUrl) {
    return Container(
      width: 150,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Image.asset(
              'assets/staticImages/cloth.png',
              fit: BoxFit.cover,
            );
          },
        ),
      ),
    );
  }
}
