import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:azblob/azblob.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/onboardingForms/GeneratingStyleGuide.dart';
import 'package:monova_ai_stylist/pages/onboardingForms/StyleGuideScreen.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class OnboardingThirdForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final String undertone;
  final String? selfieImagePath;
  final Function(String) onUndertoneSelected;
  final Function(String?) onSelfieImageUpdated;
  final VoidCallback onNext;

  const OnboardingThirdForm({
    Key? key,
    required this.formKey,
    required this.undertone,
    required this.selfieImagePath,
    required this.onUndertoneSelected,
    required this.onSelfieImageUpdated,
    required this.onNext,
  }) : super(key: key);

  @override
  State<OnboardingThirdForm> createState() => _OnboardingThirdFormState();
}

class _OnboardingThirdFormState extends State<OnboardingThirdForm> {
  final ImagePicker _picker = ImagePicker();
  String? _uploadedImageUrl;
  bool _isUploading = false;
  bool _isGeneratingStyleGuide = false;
  Map<String, dynamic>? _analysisResult;

  Future<void> _takeSelfie() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Upload Image',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'SatoshiB',
                ),
              ),
              const SizedBox(height: 24),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Color(0xFFF76037)),
                title: const Text('Take a selfie',
                    style: TextStyle(fontFamily: 'SatoshiR')),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? photo =
                      await _picker.pickImage(source: ImageSource.camera);
                  if (photo != null) {
                    setState(() {
                      _isUploading = true;
                    });
                    await _uploadImage(photo);
                  }
                },
              ),
              ListTile(
                leading:
                    const Icon(Icons.photo_library, color: Color(0xFFF76037)),
                title: const Text('Choose from gallery',
                    style: TextStyle(fontFamily: 'SatoshiR')),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image =
                      await _picker.pickImage(source: ImageSource.gallery);
                  if (image != null) {
                    setState(() {
                      _isUploading = true;
                    });
                    await _uploadImage(image);
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _uploadImage(XFile image) async {
  try {
    final storage = AzureStorage.parse(
        "DefaultEndpointsProtocol=https;AccountName=monovaoutfits;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net");
    const containerName = "user-profile-images";

    final Uint8List bytes = await image.readAsBytes();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'image_$timestamp.jpg';
    final blobPath = '/$containerName/$fileName';

    await storage.putBlob(
      blobPath,
      bodyBytes: bytes,
      contentType: 'image/jpeg',
    );

    final imageUrl = 'https://monovaoutfits.blob.core.windows.net$blobPath';
    print('Uploaded image URL: $imageUrl');

    // Analyze the uploaded image
    await _analyzeImage(imageUrl);

    if (mounted) {
      setState(() {
        _uploadedImageUrl = imageUrl;
        _isUploading = false;
      });

      // Use local path only for UI display, but store Azure URL in state
      widget.onSelfieImageUpdated(image.path);

      // Save the cloud URL to SharedPreferences
      await _saveImageUrlToPrefs(imageUrl);
      
      // Also save to SharedPrefsService
      if (imageUrl.startsWith('https://monovaoutfits.blob.core.windows.net')) {
        await SharedPrefsService.saveProfilePhoto(imageUrl);
        print('Saved profile photo URL to SharedPrefsService: $imageUrl');
      }
    }
  } catch (e) {
    print('Error uploading image: $e');
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to upload image: $e')),
      );
      setState(() {
        _isUploading = false;
        _uploadedImageUrl = null;
      });
    }
  }
}


  Future<void> _saveImageUrlToPrefs(String imageUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('userData');
      if (userData != null) {
        Map<String, dynamic> userDataMap = jsonDecode(userData);
        // Make sure we're only using the Azure URL
        if (imageUrl
            .startsWith('https://monovaoutfits.blob.core.windows.net')) {
          userDataMap["userProfileImageUrl"] = imageUrl;
          print(
              'Saved Azure cloud URL to prefs: ${userDataMap["userProfileImageUrl"]}');
          await prefs.setString('userData', jsonEncode(userDataMap));
        } else {
          print('ERROR: Attempted to save non-Azure URL: $imageUrl');
        }
      }
    } catch (e) {
      print('Error saving image URL to prefs: $e');
    }
  }

  Future<void> _analyzeImage(String imageUrl) async {
  try {
    final response = await http.post(
      Uri.parse('${ApiConstants.styleProfileApi}style-profile/analyze-image'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'url': imageUrl,
        'userUndertone': widget.undertone.toUpperCase(),
      }),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseBody = jsonDecode(response.body);

      setState(() {
        _analysisResult = responseBody;
      });

      // Only show modal sheet for invalid results
      if (responseBody.containsKey('data') &&
          responseBody['data'] != null &&
          responseBody['data']['isValid'] == false) {
        _showAnalysisResultDialog(responseBody);
      }

      // Save results if analysis was successful
      if (responseBody.containsKey('data') &&
          responseBody['data'] != null &&
          responseBody['data']['isValid'] == true) {
        _saveAnalysisResultsToPrefs(responseBody['data']);
      }
    } else {
      // Handle API errors
      Map<String, dynamic> errorResponse = {};
      try {
        errorResponse = jsonDecode(response.body);
      } catch (e) {
        errorResponse = {'message': 'Failed to analyze image'};
      }

      // Show error dialog
      _showAnalysisResultDialog({'data': {'isValid': false, 'errorMessage': errorResponse['message']}});

      throw Exception('Failed to analyze image: ${response.body}');
    }
  } catch (e) {
    print('Error analyzing image: $e');

    // Show error dialog for exceptions
    _showAnalysisResultDialog(
        {'data': {'isValid': false, 'errorMessage': 'Connection error. Please try again.'}});

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to analyze image: $e')),
      );
    }
  }
}

  Future<void> _saveAnalysisResultsToPrefs(
      Map<String, dynamic> analysisData) async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString('userData');
    if (userData != null) {
      Map<String, dynamic> userDataMap = jsonDecode(userData);
      // Update analysis-related fields in user data
      if (analysisData.containsKey('userSkinTone')) {
        userDataMap['userSkinTone'] = analysisData['userSkinTone'];
      }
      if (analysisData.containsKey('userContrast')) {
        userDataMap['userContrast'] = analysisData['userContrast'];
      }
      if (analysisData.containsKey('userHairColor')) {
        userDataMap['userHairColor'] = analysisData['userHairColor'];
      }
      if (analysisData.containsKey('userEyeColor')) {
        userDataMap['userEyeColor'] = analysisData['userEyeColor'];
      }
      if (analysisData.containsKey('userSeason')) {
        userDataMap['userSeason'] = analysisData['userSeason'];
      }

      await prefs.setString('userData', jsonEncode(userDataMap));
    }
  }

  void _showAnalysisResultDialog(Map<String, dynamic> analysisResult) {
  final data = analysisResult['data'];
  final bool isValid = data['isValid'];

  // Only show modal sheet if validation failed
  if (!isValid) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 40,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  // Text content aligned left
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // Left-align text
                      children: const [
                        Text(
                          'Uh-oh!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'SatoshiB',
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Please upload a clear selfie',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                            fontFamily: 'SatoshiR',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // Directly open camera for retaking selfie
                    _takeSelfie();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFF76037),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(48),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Try Again',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: 'SatoshiM',
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

  @override
  void initState() {
    super.initState();

    // Check if we already have a cloud URL saved
    _checkCloudUrl();
  }

  Future<void> _checkCloudUrl() async {
  // Check for saved profile photo URL
  final photoUrl = await SharedPrefsService.getProfilePhoto();
  final prefs = await SharedPreferences.getInstance();
  final userData = prefs.getString('userData');
  
  // If we have a valid photo URL from SharedPrefsService
  if (photoUrl != null && photoUrl.startsWith('https://monovaoutfits.blob.core.windows.net')) {
    setState(() {
      _uploadedImageUrl = photoUrl;
      
      // Create a mock analysis result to enable the Next button
      if (_analysisResult == null && userData != null) {
        final Map<String, dynamic> userDataMap = jsonDecode(userData);
        // Only create mock result if we have some analysis data
        if (userDataMap.containsKey('userSeason') || userDataMap.containsKey('userSkinTone')) {
          _analysisResult = {
            'data': {
              'isValid': true,
              'userSkinTone': userDataMap['userSkinTone'],
              'userSeason': userDataMap['userSeason'],
              'userContrast': userDataMap['userContrast'],
              'userHairColor': userDataMap['userHairColor'],
              'userEyeColor': userDataMap['userEyeColor']
            }
          };
        }
      }
    });
    print('Loaded profile photo URL from SharedPrefsService: $photoUrl');
  }
  
  // Also check userData as a fallback
  else if (userData != null) {
    final Map<String, dynamic> userDataMap = jsonDecode(userData);
    final storedUrl = userDataMap["userProfileImageUrl"];
    
    if (storedUrl != null && storedUrl.toString().startsWith('https://monovaoutfits.blob.core.windows.net')) {
      setState(() {
        _uploadedImageUrl = storedUrl;
        
        // Create a mock analysis result if we have saved analysis data
        if (_analysisResult == null) {
          // Only create mock result if we have some analysis data
          if (userDataMap.containsKey('userSeason') || userDataMap.containsKey('userSkinTone')) {
            _analysisResult = {
              'data': {
                'isValid': true,
                'userSkinTone': userDataMap['userSkinTone'],
                'userSeason': userDataMap['userSeason'],
                'userContrast': userDataMap['userContrast'],
                'userHairColor': userDataMap['userHairColor'],
                'userEyeColor': userDataMap['userEyeColor']
              }
            };
          }
        }
      });
      print('Loaded valid Azure URL from userData: $storedUrl');
    }
  }
}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Form(
        key: widget.formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          children: [
            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    const Text(
                      'What is your undertone?',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'SatoshiR',
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Tip: Check the vein colour and natural light and pick what matches.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontFamily: 'SatoshiR',
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildUndertoneOptions(),
                    if (widget.undertone.isEmpty)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Please select your undertone',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                            fontFamily: 'SatoshiR',
                          ),
                        ),
                      ),
                    const SizedBox(height: 32),
                    const Text(
                      'Let\'s take a selfie?',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'SatoshiR',
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'A selfie lets us determine things like eye colour and hair colour. Not just that but also contrast and shadows.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontFamily: 'SatoshiR',
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildSelfieUploader(),
                    if (widget.selfieImagePath == null)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Please take a selfie',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                            fontFamily: 'SatoshiR',
                          ),
                        ),
                      ),
                    const SizedBox(height: 60), // Extra space for button area
                  ],
                ),
              ),
            ),

            // Fixed button area at the bottom
            Container(
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: _buildNavigationButtons(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUndertoneOptions() {
    return Row(
      children: [
        Expanded(
          child: _buildUndertoneCard(
            'Cool Undertone',
            'Blue-Purple Veins',
            'Cool',
            'assets/onboardingForm/Hand blue-purple.png',
            const [Color(0xFF3F51B5), Color(0xFF5C6BC0), Color(0xFF7986CB)],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildUndertoneCard(
            'Warm Undertone',
            'Green Veins',
            'Warm',
            'assets/onboardingForm/Hand green.png',
            const [Color(0xFF8BC34A), Color(0xFF9CCC65)],
          ),
        ),
      ],
    );
  }

  Widget _buildUndertoneCard(
      String title, String smallText, String value, String imagePath, List<Color> colorDots) {
    final isSelected = widget.undertone == value;

    return GestureDetector(
      onTap: () => widget.onUndertoneSelected(value),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFFF76037) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                imagePath,
                height: 120,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: colorDots
                  .map((color) => Container(
                        width: 16,
                        height: 16,
                        margin: const EdgeInsets.only(right: 4),
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                        ),
                      ))
                  .toList(),
            ),
            const SizedBox(height: 8),
            Text(
              smallText,
              style: TextStyle(
                fontFamily: 'SatoshiR',
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontFamily: 'SatoshiR',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF4A4A4A),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelfieUploader() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: GestureDetector(
            onTap: _takeSelfie, // Now shows both camera and gallery options
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: widget.selfieImagePath != null ? null : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _isUploading
                  ? const Center(child: CircularProgressIndicator())
                  : (widget.selfieImagePath == null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color:
                                      const Color(0xFFF76037).withOpacity(0.2),
                                ),
                                child: const Icon(
                                  Icons.add,
                                  color: Color(0xFFF76037),
                                  size: 32,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                "Upload image",
                                style: TextStyle(
                                  color: Color(0xFFF76037),
                                  fontFamily: 'SatoshiR',
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: FileImage(File(widget.selfieImagePath!)),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        )),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '💡 Tip',
                style: TextStyle(
                  fontFamily: 'SatoshiB',
                  fontSize: 16,
                  color: Color(0xFFF76037),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Try taking the selfie in bright natural light and try to ensure that the face is evenly lit.',
                style: TextStyle(
                  fontFamily: 'SatoshiR',
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons() {
  // Just check if we have both undertone and selfie image
  bool isFormValid = widget.undertone.isNotEmpty && widget.selfieImagePath != null;
  
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Expanded(
        child: ElevatedButton(
          onPressed: isFormValid
              ? () {
                  FocusScope.of(context).unfocus();
                  widget.onNext();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF76037),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(48),
            ),
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text(
            'Next',
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'SatoshiM',
              fontSize: 16,
            ),
          ),
        ),
      ),
    ],
  );
}
}
