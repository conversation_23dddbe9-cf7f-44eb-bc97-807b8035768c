import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:hive/hive.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/models/chatModels/message.dart';
import 'package:monova_ai_stylist/pages/chatScreen/chatScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/newOutfitScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart'
    as outfit_screen;
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart';
import 'package:monova_ai_stylist/pages/homeScreen/homeScreenWidgets/footerSection.dart';
import 'package:monova_ai_stylist/pages/homeScreen/homeScreenWidgets/handpickedSection.dart';
import 'package:monova_ai_stylist/pages/homeScreen/homeScreenWidgets/weeklyOutfitsSection.dart';
import 'package:monova_ai_stylist/services/shakeService/reportDialogManager.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/services/weatherservice.dart';
import 'package:monova_ai_stylist/services/websocketService.dart';
import 'package:shimmer/shimmer.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class Outfit {
  final String userId;
  final String outfitId;
  final String outfitName;
  final List<String> apparelPointers;
  final String dateCreated;
  final String status;
  final List<String> tags;
  List<Apparel> apparels = [];
  String description;

  Outfit({
    required this.userId,
    required this.outfitId,
    required this.outfitName,
    required this.apparelPointers,
    required this.dateCreated,
    required this.status,
    required this.tags,
    this.description = '',
  });

  factory Outfit.fromJson(Map<String, dynamic> json) {
    return Outfit(
      userId: json['userId'],
      outfitId: json['outfitId'],
      outfitName: json['outfitName'],
      apparelPointers: List<String>.from(json['apparelPointers']),
      dateCreated: json['dateCreated'],
      status: json['status'],
      tags: List<String>.from(json['tags']),
    );
  }
}

class Apparel {
  final String apparelId;
  final String apparelType;
  final String apparelMediaUrl;
  final String productName;
  final String brand;
  final String colour;

  Apparel({
    required this.apparelId,
    required this.apparelType,
    required this.apparelMediaUrl,
    required this.productName,
    required this.brand,
    required this.colour,
  });

  factory Apparel.fromJson(Map<String, dynamic> json) {
  return Apparel(
    apparelId: json['data']['apparelId'] ?? '',
    apparelType: json['data']['apparelType'] ?? '',
    apparelMediaUrl: json['data']['apparelMediaUrl'] ?? '',
    productName: json['data']['productName'] ?? '',
    brand: json['data']['brand'] ?? '', 
    colour: json['data']['colour'] ?? '',
  );
}
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    ReportManager().initialize(context);
  }

  @override
  void dispose() {
    ReportManager().dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    const _HeaderSection(),
                    const SizedBox(height: 20),
                    // const OnboardingProgressCard(), // Add the new card here
                    const _ExploreSection(),
                    const SizedBox(height: 34),
                    WeeklyOutfitsSection(), // Add this new section here
                    const SizedBox(height: 34),
                    HandpickedSection(),
                    const SizedBox(height: 34),
                    const _OutfitsSection(),
                    // const SizedBox(height: 34),
                    // StyleTipsSection(),
                    // const SizedBox(height: 34),
                    Divider(),
                  ],
                ),
              ),
              const SizedBox(height: 34),
              FooterSection(),
            ],
          ),
        ),
      ),
    );
  }
}

class _HeaderSection extends StatefulWidget {
  const _HeaderSection({Key? key}) : super(key: key);

  @override
  State<_HeaderSection> createState() => _HeaderSectionState();
}

class _HeaderSectionState extends State<_HeaderSection> {
  String userName = 'User';
  String weatherCondition = '--';
  String location = '--';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadWeatherData();
  }

  Future<void> _loadUserData() async {
    try {
      final userId = await SharedPrefsService.getUserId();

      if (userId == null) {
        setState(() {
          userName = 'User';
        });
        return;
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.userApi}user'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({"operation": "get", "userId": userId}),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final fullName = jsonResponse['data']['userFullName'];

        // Store in SharedPreferences for future use
        await SharedPrefsService.saveFullName(fullName);

        if (mounted) {
          setState(() {
            userName = fullName;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            userName = 'User';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          userName = 'User';
        });
      }
    }
  }

  Future<void> _loadWeatherData() async {
    setState(() {
      isLoading = true;
    });

    try {
      final weatherData = await WeatherLocationService.getWeatherData();

      if (mounted) {
        print("Weather condition being set: ${weatherData.condition}");
        setState(() {
          weatherCondition = weatherData.condition;
          location = weatherData.location;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          weatherCondition = ''; 
          location = '';
          isLoading = false;
        });
      }
    }
  }

  IconData _getWeatherIcon(String condition) {
    condition = condition.toLowerCase();

    if (condition.contains('clear') || condition.contains('sunny')) {
      return FeatherIcons.sun;
    } else if (condition.contains('cloud')) {
      return FeatherIcons.cloud;
    } else if (condition.contains('rain') || condition.contains('drizzle')) {
      return FeatherIcons.cloudRain;
    } else if (condition.contains('thunder') || condition.contains('storm')) {
      return FeatherIcons.cloudLightning;
    } else if (condition.contains('snow')) {
      return FeatherIcons.cloudSnow;
    } else if (condition.contains('mist') || condition.contains('fog')) {
      return FeatherIcons.cloud;
    } else if (condition.contains('haze') || condition.contains('smoke')) {
      return FeatherIcons.wind;
    } else {
      return FeatherIcons.sun; // default
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            isLoading
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmer(height: 14, width: 200),
                      const SizedBox(height: 4),
                      _buildShimmer(height: 14, width: 180),
                    ],
                  )
                : const Text(
                    'Welcome back',
                    style: TextStyle(
                      fontFamily: "SatoshiM",
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
            const SizedBox(height: 4),
            isLoading
                ? _buildShimmer(height: 24, width: 150)
                : Text(
                    '${userName.split(' ').first}',
                    style: const TextStyle(
                      fontFamily: "SatoshiR",
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 0, 6, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                children: [
                  const Icon(FeatherIcons.mapPin, size: 12),
                  const SizedBox(width: 4),
                  isLoading
                      ? _buildShimmer(height: 14, width: 40)
                      : Text(
                          location,
                          style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: "SatoshiR",
                            fontSize: 12,
                          ),
                        ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  isLoading
                      ? const Icon(FeatherIcons.sun, size: 12)
                      : Icon(_getWeatherIcon(weatherCondition), size: 12),
                  const SizedBox(width: 4),
                  isLoading
                      ? _buildShimmer(height: 14, width: 40)
                      : Text(
                          weatherCondition,
                          style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: "SatoshiR",
                            fontSize: 12,
                          ),
                        ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShimmer({required double height, required double width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }
}

class OnboardingProgressCard extends StatefulWidget {
  const OnboardingProgressCard({Key? key}) : super(key: key);

  @override
  State<OnboardingProgressCard> createState() => _OnboardingProgressCardState();
}

class _OnboardingProgressCardState extends State<OnboardingProgressCard> {
  bool isVisible = true;

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 34),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7F5), // Light pink background
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      isVisible = false;
                    });
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: const Color(0xFFEEEEEE),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.black54,
                      size: 18,
                    ),
                  ),
                ),
              ],
            ),

            // Progress indicator
            const Center(
              child: SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  value: 0.33, // Set to 1/3 complete
                  strokeWidth: 5,
                  backgroundColor: Colors.white,
                  valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFF76037)), // Orange color
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Heading
            const Center(
              child: Text(
                "You're so close!",
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 8),

            // Subheading
            Center(
              child: RichText(
                textAlign: TextAlign.center,
                text: const TextSpan(
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  children: [
                    TextSpan(
                      text: "Let's get Monova tuned to your style magic. ✨",
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Checklist items
            _buildProgressItem(
              text: "Create your profile",
              isCompleted: true,
            ),

            const SizedBox(height: 16),

            _buildProgressItem(
              text: "Add 6 relevant items to wardrobe",
              isCompleted: false,
            ),

            const SizedBox(height: 16),

            _buildProgressItem(
              text: "Have your first chat",
              isCompleted: false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem({required String text, required bool isCompleted}) {
    return Row(
      children: [
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontFamily: "SatoshiR",
              fontSize: 14,
              color: isCompleted ? Colors.black : Colors.black,
              decoration: isCompleted ? TextDecoration.lineThrough : null,
            ),
          ),
        ),
        Container(
          width: 18,
          height: 18,
          decoration: BoxDecoration(
            color: isCompleted ? const Color(0xFFF76037) : Colors.white,
            border: Border.all(
              color: isCompleted
                  ? const Color(0xFFF76037)
                  : const Color(0xFFCCCCCC),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: isCompleted
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                )
              : null,
        ),
      ],
    );
  }
}

class _ContinueItemCard extends StatefulWidget {
  final String title;
  final String subtitle;
  final bool isFirst;

  const _ContinueItemCard({
    required this.title,
    required this.subtitle,
    this.isFirst = false,
  });

  @override
  State<_ContinueItemCard> createState() => _ContinueItemCardState();
}

class _ContinueItemCardState extends State<_ContinueItemCard> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Simulate loading for demonstration
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.8,
      margin: EdgeInsets.only(right: 16, left: widget.isFirst ? 0 : 0),
      decoration: BoxDecoration(
        color: Color(0xffF8F7F7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isLoading
          ? _buildShimmerContent()
          : Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      'assets/staticImages/pick.png',
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[300],
                        child: const Icon(Icons.broken_image),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.subtitle,
                        style: const TextStyle(
                          fontFamily: "SatoshiR",
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.title,
                        style: const TextStyle(
                          fontFamily: "SatoshiB",
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildShimmerContent() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 14,
                    width: 100,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 16,
                    width: 150,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ExploreSection extends StatefulWidget {
  const _ExploreSection({Key? key}) : super(key: key);

  @override
  State<_ExploreSection> createState() => _ExploreSectionState();
}

class _ExploreSectionState extends State<_ExploreSection> {
  bool isLoading = true;
  List<Map<String, dynamic>> exploreItems = [];
  late Box exploreBox;
  final String exploreBoxName = 'explore_items_cache';
  final String lastExploreFetchKey = 'last_explore_fetch_time';
  final String midnightClearKey = 'last_midnight_clear_time';
  Timer? _midnightTimer;
  final Duration cacheValidDuration = Duration(hours: 12); // Cache valid for 12 hours

  @override
  void initState() {
    super.initState();
    _initHive();
    _setupMidnightTimer();
  }

   Future<void> _initHive() async {
    try {
      // Initialize Hive box
      if (!Hive.isBoxOpen(exploreBoxName)) {
        await Hive.openBox(exploreBoxName);
      }
      exploreBox = Hive.box(exploreBoxName);

      // Check if we need to clear the cache
      await _checkMidnightReset();

      // Load data from cache or network
      await loadCachedExploreItems();
    } catch (e) {
      print("Error initializing Hive for explore items: $e");
      // Fall back to network fetch if Hive initialization fails
      _fetchExploreItems();
    }
  }

  void _setupMidnightTimer() {
    // Cancel existing timer if it exists
    _midnightTimer?.cancel();
    
    // Schedule timer for next midnight
    DateTime now = DateTime.now();
    DateTime nextMidnight = DateTime(now.year, now.month, now.day + 1);
    Duration timeUntilMidnight = nextMidnight.difference(now);
    
    _midnightTimer = Timer(timeUntilMidnight, () {
      // Clear cache and fetch new data at midnight
      _clearCacheAndFetch();
      // Setup the timer for the next midnight
      _setupMidnightTimer();
    });
    
    print("Explore midnight timer scheduled. Next reset in ${timeUntilMidnight.inHours} hours, ${timeUntilMidnight.inMinutes % 60} minutes");
  }

  Future<void> _checkMidnightReset() async {
    try {
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      
      // Check if we've already cleared the cache today
      if (exploreBox.containsKey(midnightClearKey)) {
        String lastClearDate = exploreBox.get(midnightClearKey);
        
        if (lastClearDate != today) {
          // It's a new day, clear the cache
          await _clearCacheAndFetch();
          return;
        }
      } else {
        // First time running or key doesn't exist, set it
        await exploreBox.put(midnightClearKey, today);
      }
    } catch (e) {
      print("Error checking midnight reset for explore items: $e");
    }
  }

  Future<void> loadCachedExploreItems() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Check if we have cached data and if it's still valid
      if (exploreBox.containsKey('explore_items_data') && 
          exploreBox.containsKey(lastExploreFetchKey)) {
        final lastFetch = DateTime.parse(exploreBox.get(lastExploreFetchKey));
        final now = DateTime.now();

        // If cache is still valid, use it
        if (now.difference(lastFetch) < cacheValidDuration) {
          final cachedData = exploreBox.get('explore_items_data');
          List<dynamic> cachedItems = cachedData['items'] ?? [];
          
          List<Map<String, dynamic>> items = [];
          for (var item in cachedItems) {
            try {
              items.add({
                'title': item['title'] ?? '',
                'image': item['image'] ?? '',
                'useCase': item['useCase'] ?? '',
              });
            } catch (e) {
              print("Error parsing cached explore item: $e");
            }
          }
          
          setState(() {
            exploreItems = items;
            isLoading = false;
          });
          
          print("Using cached explore items data");
          return;
        }
      }

      // If no valid cache, fetch from network
      await _fetchExploreItems();
    } catch (e) {
      print("Error loading cached explore items: $e");
      setState(() {
        isLoading = false;
      });

      // Always fetch from network if there's an error with the cache
      await _fetchExploreItems();
    }
  }

  Future<void> _clearCacheAndFetch() async {
    try {
      // Clear explore data
      if (exploreBox.containsKey('explore_items_data')) {
        await exploreBox.delete('explore_items_data');
      }
      
      // Clear last fetch time
      if (exploreBox.containsKey(lastExploreFetchKey)) {
        await exploreBox.delete(lastExploreFetchKey);
      }
      
      // Update the last clear date
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      await exploreBox.put(midnightClearKey, today);
      
      print("Explore cache cleared at midnight. Fetching new items...");
      
      // Fetch new items
      await _fetchExploreItems();
    } catch (e) {
      print("Error clearing explore cache at midnight: $e");
    }
  }


  Future<void> _fetchExploreItems() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${ApiConstants.styleProfileApi}style-profile/get-prompts'),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData.containsKey('data') && responseData['data'] is List) {
          List<Map<String, dynamic>> fetchedItems = List<Map<String, dynamic>>.from(
            responseData['data'].map((item) => {
                  'title': item['Prompt'],
                  'image': item['Image'],
                  'useCase': item['UseCase'],
                }),
          );

          // Cache the fetched data
          Map<String, dynamic> cacheData = {
            'items': fetchedItems,
          };
          
          await exploreBox.put('explore_items_data', cacheData);
          await exploreBox.put(lastExploreFetchKey, DateTime.now().toIso8601String());

          setState(() {
            exploreItems = fetchedItems;
            isLoading = false;
          });
        } else {
          _loadFallbackData();
        }
      } else {
        // Fallback to sample data if the API fails
        _loadFallbackData();
      }
    } catch (e) {
      print("Error fetching explore items: $e");
      _loadFallbackData();
    }
  }

  void _loadFallbackData() {
    List<Map<String, dynamic>> fallbackItems = [
      {
        'title': 'Have your office outfit ready.',
        'image': 'assets/staticImages/explore.png',
        'useCase': 'CURATION',
      },
      {
        'title': 'Add new dress to wardrobe.',
        'image': 'assets/staticImages/explore.png',
        'useCase': 'WARDROBE',
      },
      {
        'title': 'Get your wedding guest outfit.',
        'image': 'assets/staticImages/explore.png',
        'useCase': 'CURATION',
      },
      {
        'title': 'Create custom style guide.',
        'image': 'assets/staticImages/explore.png',
        'useCase': 'CURATION',
      },
      {
        'title': 'Discover seasonal trends.',
        'image': 'assets/staticImages/explore.png',
        'useCase': 'CURATION',
      },
    ];

    try {
      // Cache the fallback data too
      Map<String, dynamic> cacheData = {
        'items': fallbackItems,
      };
      
      exploreBox.put('explore_items_data', cacheData);
      exploreBox.put(lastExploreFetchKey, DateTime.now().toIso8601String());
    } catch (e) {
      print("Error caching fallback data: $e");
    }

    setState(() {
      exploreItems = fallbackItems;
      isLoading = false;
    });
  }

    @override
  void dispose() {
    _midnightTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Explore Monova',
          style: TextStyle(
            fontFamily: "SatoshiB",
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Dive in and see all the cool ways Monova can level up your style game!',
          style: TextStyle(
            fontFamily: "SatoshiM",
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 160,
          child: isLoading
              ? ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding:
                          EdgeInsets.only(right: 16, left: index == 0 ? 0 : 0),
                      child: _buildShimmerExploreCard(),
                    );
                  },
                )
              : ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: exploreItems.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding:
                          EdgeInsets.only(right: 16, left: index == 0 ? 0 : 0),
                      child: _buildExploreCard(
                        title: exploreItems[index]['title'],
                        imageUrl: exploreItems[index]['image'],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildShimmerExploreCard() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: 140,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildExploreCard({required String title, required String imageUrl}) {
    // List of gradient colors
    final List<Color> gradientColors = [
      const Color(0xFFEBFCEC), // Light green
      const Color(0xFFEDECFD), // Light purple
      const Color(0xFFFDF0EC), // Light coral
      const Color(0xFFFFFAE9), // Light yellow
      const Color(0xFFFDECF7), // Light pink
      const Color(0xFFEBF7FC), // Light blue
    ];

    // Get a color based on the title's hashcode to ensure consistent colors
    final int colorIndex = title.hashCode.abs() % gradientColors.length;
    final Color cardColor = gradientColors[colorIndex];

    return GestureDetector(
      onTap: () {
        // First show the chat screen modal, THEN send the message
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          clipBehavior: Clip.antiAliasWithSaveLayer,
          builder: (context) {
            // Create ChatScreen with initialMessages
            final chatScreen = ChatScreen(
              initialMessages: [
                Message(
                  isUser: true,
                  message: title,
                  date: DateTime.now(),
                  isComplete: true,
                ),
              ],
            );

            // After a short delay to ensure ChatScreen is initialized
            // and its WebSocket listener is set up, then send the message
            Future.delayed(const Duration(milliseconds: 300), () {
              WebSocketService.instance.sendMessage(title);
            });

            return Container(
              height: MediaQuery.of(context).size.height * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: chatScreen,
            );
          },
        );
      },
      child: Container(
        width: 140,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: cardColor,
          image: imageUrl.isNotEmpty
              ? DecorationImage(
                  image: imageUrl.startsWith('http')
                      ? NetworkImage(imageUrl) as ImageProvider
                      : AssetImage(imageUrl),
                  fit: BoxFit.cover,
                )
              : null,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              stops: [0.25, 1.0], // Smoother transition
              colors: [
                cardColor.withOpacity(1), // Slightly transparent card color
                cardColor.withOpacity(
                    0.1), // Faint fade instead of full transparency
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Align(
              alignment: Alignment.bottomLeft,
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  color: Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _OutfitsSection extends StatefulWidget {
  const _OutfitsSection({Key? key}) : super(key: key);

  @override
  State<_OutfitsSection> createState() => _OutfitsSectionState();
}

class _OutfitsSectionState extends State<_OutfitsSection> {
  int _currentOutfitIndex = 0;
  bool isLoading = true;
  List<Outfit> outfits = [];
  List<OutfitCollection> collections = [];
  bool isCollectionLoading = false;
  Outfit? currentOutfit;
  TextEditingController newCollectionController = TextEditingController();
  String? _userGender;
  late Box outfitsBox;
  final String outfitsBoxName = 'daily_outfits';
  final String lastOutfitsFetchKey = 'last_outfits_fetch_time';
  final String midnightClearKey = 'last_midnight_clear_time';
  DateTime? _lastMidnightCheck;
  Timer? _midnightTimer;
  final Duration cacheValidDuration = Duration(hours: 12); // Cache valid for 12 hours

  @override
  void initState() {
    super.initState();
    ReportManager().initialize(context);
    _initHive();
    _fetchUserGender();
    // Start the timer to check for midnight reset
    _setupMidnightTimer();
  }

  Future<void> _initHive() async {
    try {
      // Initialize Hive box
      if (!Hive.isBoxOpen(outfitsBoxName)) {
        await Hive.openBox(outfitsBoxName);
      }
      outfitsBox = Hive.box(outfitsBoxName);

      // Check if we need to clear the cache
      await _checkMidnightReset();

      // Load data from cache or network
      await loadCachedOutfits();
    } catch (e) {
      print("Error initializing Hive for daily outfits: $e");
      // Fall back to network fetch if Hive initialization fails
      fetchTodayOutfits();
    }
  }

  void _setupMidnightTimer() {
    // Cancel existing timer if it exists
    _midnightTimer?.cancel();
    
    // Schedule timer for next midnight
    DateTime now = DateTime.now();
    DateTime nextMidnight = DateTime(now.year, now.month, now.day + 1);
    Duration timeUntilMidnight = nextMidnight.difference(now);
    
    _midnightTimer = Timer(timeUntilMidnight, () {
      // Clear cache and fetch new data at midnight
      _clearCacheAndFetch();
      // Setup the timer for the next midnight
      _setupMidnightTimer();
    });
    
    print("Midnight timer scheduled. Next reset in ${timeUntilMidnight.inHours} hours, ${timeUntilMidnight.inMinutes % 60} minutes");
  }

  Future<void> _checkMidnightReset() async {
    try {
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      
      // Check if we've already cleared the cache today
      if (outfitsBox.containsKey(midnightClearKey)) {
        String lastClearDate = outfitsBox.get(midnightClearKey);
        
        if (lastClearDate != today) {
          // It's a new day, clear the cache
          await _clearCacheAndFetch();
          return;
        }
      } else {
        // First time running or key doesn't exist, set it
        await outfitsBox.put(midnightClearKey, today);
      }
      
      _lastMidnightCheck = now;
    } catch (e) {
      print("Error checking midnight reset: $e");
    }
  }

  Future<void> _clearCacheAndFetch() async {
    try {
      // Clear outfit data
      if (outfitsBox.containsKey('daily_outfits_data')) {
        await outfitsBox.delete('daily_outfits_data');
      }
      
      // Clear last fetch time
      if (outfitsBox.containsKey(lastOutfitsFetchKey)) {
        await outfitsBox.delete(lastOutfitsFetchKey);
      }
      
      // Update the last clear date
      DateTime now = DateTime.now();
      String today = DateFormat('yyyy-MM-dd').format(now);
      await outfitsBox.put(midnightClearKey, today);
      
      print("Cache cleared at midnight. Fetching new outfits...");
      
      // Fetch new outfits
      await fetchTodayOutfits();
    } catch (e) {
      print("Error clearing cache at midnight: $e");
    }
  }

  Future<void> loadCachedOutfits() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Check if we have cached data and if it's still valid
      if (outfitsBox.containsKey('daily_outfits_data') && 
          outfitsBox.containsKey(lastOutfitsFetchKey)) {
        final lastFetch = DateTime.parse(outfitsBox.get(lastOutfitsFetchKey));
        final now = DateTime.now();

        // If cache is still valid, use it
        if (now.difference(lastFetch) < cacheValidDuration) {
          final cachedData = outfitsBox.get('daily_outfits_data');
          List<dynamic> cachedOutfitsRaw = cachedData['outfits'] ?? [];
          
          List<Outfit> cachedOutfits = [];
          for (var outfitData in cachedOutfitsRaw) {
            try {
              Outfit outfit = Outfit(
                userId: outfitData['userId'] ?? '',
                outfitId: outfitData['outfitId'] ?? '',
                outfitName: outfitData['outfitName'] ?? '',
                apparelPointers: List<String>.from(outfitData['apparelPointers'] ?? []),
                dateCreated: outfitData['dateCreated'] ?? '',
                status: outfitData['status'] ?? '',
                tags: List<String>.from(outfitData['tags'] ?? []),
                description: outfitData['description'] ?? '',
              );
              
              // Restore apparels if they exist
              if (outfitData['apparels'] != null && outfitData['apparels'] is List) {
                for (var apparelData in outfitData['apparels']) {
                  Apparel apparel = Apparel(
                    apparelId: apparelData['apparelId'] ?? '',
                    apparelType: apparelData['apparelType'] ?? '',
                    apparelMediaUrl: apparelData['apparelMediaUrl'] ?? '',
                    productName: apparelData['productName'] ?? '',
                    brand: apparelData['brand'] ?? '',
                    colour: apparelData['colour'] ?? '',
                  );
                  outfit.apparels.add(apparel);
                }
              }
              
              cachedOutfits.add(outfit);
            } catch (e) {
              print("Error parsing cached outfit: $e");
            }
          }
          
          setState(() {
            outfits = cachedOutfits;
            isLoading = false;
          });
          
          print("Using cached daily outfits data");
          
          // For outfits that don't have apparel details, fetch them
          for (var outfit in cachedOutfits) {
            if (outfit.apparelPointers.isNotEmpty && outfit.apparels.isEmpty) {
              await fetchApparelDetails(outfit);
            }
          }
          
          // Optionally fetch latest data in background for next time
          _fetchAndCacheInBackground();
          return;
        }
      }

      // If no valid cache, fetch from network
      await fetchTodayOutfits();
    } catch (e) {
      print("Error loading cached outfits: $e");
      setState(() {
        isLoading = false;
      });

      // Always fetch from network if there's an error with the cache
      await fetchTodayOutfits();
    }
  }

    Future<void> _fetchAndCacheInBackground() async {
    // Fetch in background without loading indicators
    try {
      await fetchTodayOutfits(showLoading: false);
    } catch (e) {
      print("Background fetch of daily outfits failed: $e");
    }
  }

  Future<void> _fetchUserGender() async {
    try {
      // Get user ID from shared preferences
      final userId = await SharedPrefsService.getUserId();

      if (userId == null) {
        return;
      }

      // Fetch style profile from server to get updated data
      final authToken = await SharedPrefsService.getAuthToken();

      if (authToken == null) {
        return;
      }

      final response = await http.get(
        Uri.parse(
            '${ApiConstants.styleProfileApi}style-profile?userId=$userId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final userData = jsonResponse['data'];

        if (userData != null && userData['userGender'] != null) {
          final gender = userData['userGender'];

          setState(() {
            _userGender = gender;
          });
        }
      }
    } catch (e) {
      print('Error fetching user gender: $e');
    }
  }

  Future<void> fetchTodayOutfits({bool showLoading = true}) async {
    if (showLoading) {
      setState(() {
        isLoading = true;
      });
    }

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      // Get weather data to pass to the API
      final weatherData = await WeatherLocationService.getWeatherData();
      String weatherCondition = weatherData.condition;

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isLoading = false;
        });
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/get-today-outfits?userId=$userId&weather=$weatherCondition'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        List<Outfit> fetchedOutfits = [];
        if (jsonResponse['data'] != null) {
          Map<String, dynamic> outfitsByCategory = jsonResponse['data'];
          outfitsByCategory.forEach((category, outfitData) {
            Outfit outfit = Outfit(
              userId: outfitData['userId'],
              outfitId: outfitData['outfitId'],
              outfitName: outfitData['outfitName'],
              apparelPointers: List<String>.from(outfitData['apparelPointers']),
              dateCreated: outfitData['dateCreated'],
              status: outfitData['status'],
              tags: List<String>.from(outfitData['tags']),
              description: 'Category: ${category.toUpperCase()}',
            );
            fetchedOutfits.add(outfit);
          });
        }

        // For each outfit, fetch the apparel details
        for (var outfit in fetchedOutfits) {
          if (outfit.apparelPointers.isNotEmpty) {
            await fetchApparelDetails(outfit);
          }
        }

        // Prepare data for caching - serialize outfits
        List<Map<String, dynamic>> outfitsForCache = fetchedOutfits.map((outfit) {
          // Convert Outfit to Map
          Map<String, dynamic> outfitMap = {
            'userId': outfit.userId,
            'outfitId': outfit.outfitId,
            'outfitName': outfit.outfitName,
            'apparelPointers': outfit.apparelPointers,
            'dateCreated': outfit.dateCreated,
            'status': outfit.status,
            'tags': outfit.tags,
            'description': outfit.description,
            'apparels': outfit.apparels.map((apparel) => {
              'apparelId': apparel.apparelId,
              'apparelType': apparel.apparelType,
              'apparelMediaUrl': apparel.apparelMediaUrl,
              'productName': apparel.productName,
              'brand': apparel.brand,
              'colour': apparel.colour,
            }).toList(),
          };
          return outfitMap;
        }).toList();

        // Cache the data
        Map<String, dynamic> cacheData = {
          'outfits': outfitsForCache,
        };
        
        await outfitsBox.put('daily_outfits_data', cacheData);
        await outfitsBox.put(lastOutfitsFetchKey, DateTime.now().toIso8601String());

        setState(() {
          outfits = fetchedOutfits;
          isLoading = false;
        });
      } else {
        print("Error fetching today's outfits: ${response.reasonPhrase}");
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching today's outfits: $e");
      setState(() {
        isLoading = false;
      });
    }
  }


  void _showSaveOutfitBottomSheet(BuildContext context, [Outfit? outfit]) {
    // Store the current outfit to use in API calls
    currentOutfit = outfit ??
        (_currentOutfitIndex < outfits.length
            ? outfits[_currentOutfitIndex]
            : null);

    // If no outfit is available, show an error
    if (currentOutfit == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No outfit to save'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Fetch collections before showing the bottom sheet
    fetchCollections().then((_) {
      showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Save to',
                        style: TextStyle(
                          fontFamily: 'SatoshiM',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 24),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: isCollectionLoading
                        ? const SizedBox()
                        : SingleChildScrollView(
                            child: Column(
                              children: [
                                _buildSaveOptionRow(
                                  title: 'New Style Group',
                                  outfitCount: 0,
                                  isNew: true,
                                  onTap: () {
                                    _showNewStyleGroupModal(context);
                                  },
                                ),
                                if (collections.isNotEmpty)
                                  ...collections.map((collection) {
                                    return _buildSaveOptionRow(
                                      title: collection.outfitCollectionName,
                                      outfitCount:
                                          collection.outfitPointers.length,
                                      collection: collection,
                                      onTap: () {
                                        saveOutfitToCollection(
                                            collection, currentOutfit!);
                                      },
                                    );
                                  }).toList(),
                              ],
                            ),
                          ),
                  )
                ],
              ),
            );
          });
        },
      );
    });
  }

  Widget _buildSaveOptionRow({
    required String title,
    required int outfitCount,
    required VoidCallback onTap,
    bool isNew = false,
    OutfitCollection? collection,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 83,
            height: 83,
            decoration: BoxDecoration(
              color: isNew ? Colors.white : _getColorForCollection(title),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: isNew
                  ? const Icon(Icons.add, color: Colors.black, size: 24)
                  : (collection != null &&
                          collection.outfitCollectionMediaUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Image.network(
                            collection.outfitCollectionMediaUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              _getIconForCollection(title),
                              size: 24,
                              color: Colors.black,
                            ),
                          ),
                        )
                      : Icon(_getIconForCollection(title),
                          size: 24, color: Colors.black)),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$outfitCount outfits',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            backgroundColor: Colors.grey[200],
            child: IconButton(
              icon: const Icon(Icons.add, color: Colors.grey),
              onPressed: onTap,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return Icons.work_outline;
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return Icons.favorite;
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return Icons.wb_sunny_outlined;
    }
    return Icons.style;
  }

  Color _getColorForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return const Color(0xFFFFF8E7);
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return const Color(0xFFFFF1F1);
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return const Color(0xFFF5F1FF);
    }
    return Colors.white;
  }

  void _showNewStyleGroupModal(BuildContext context) {
    // Create a local controller that won't be disposed with the screen
    TextEditingController localController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'New Style Group',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 24),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  height: 200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.help_outline,
                            color: Colors.grey,
                            size: 50,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'The image is auto generated\nbased on the name',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Journal Name',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: localController,
                  decoration: InputDecoration(
                    hintText: 'Party at Night, Fun day out...',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (localController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Please enter a name for your style group'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // Store the value before closing modal
                      final collectionName = localController.text.trim();

                      // Close this modal
                      Navigator.pop(context);

                      // Create collection WITHOUT requiring an outfit
                      createCollection(collectionName);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xffF76037),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> fetchCollections() async {
    setState(() {
      isCollectionLoading = true;
    });

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isCollectionLoading = false;
        });
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/list?userId=$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        print("Collections response: $responseBody"); // For debugging

        List<OutfitCollection> fetchedCollections = [];
        if (jsonResponse['data'] != null) {
          for (var collectionJson in jsonResponse['data']) {
            OutfitCollection collection =
                OutfitCollection.fromJson(collectionJson);
            fetchedCollections.add(collection);
          }
        }

        setState(() {
          collections = fetchedCollections;
          isCollectionLoading = false;
        });
      } else {
        print("Error fetching collections: ${response.reasonPhrase}");
        setState(() {
          isCollectionLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching collections: $e");
      setState(() {
        isCollectionLoading = false;
      });
    }
  }

  Future<void> createCollection(String collectionName, [Outfit? outfit]) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'POST',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections?userId=$userId'));

      // Create the request body based on whether an outfit is provided
      var requestBody = {
        "outfitCollectionName": collectionName,
        "outfitPointers": outfit != null ? [outfit.outfitId] : []
      };

      request.body = jsonEncode(requestBody);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 201) {
        String responseBody = await response.stream.bytesToString();
        print("Collection created successfully: $responseBody");

        // Refresh collections list
        await fetchCollections();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Collection created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error creating collection: ${response.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print("Exception when creating collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> saveOutfitToCollection(
      OutfitCollection collection, Outfit outfit) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      // Check if outfit is already in the collection
      if (collection.outfitPointers.contains(outfit.outfitId)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Outfit already in ${collection.outfitCollectionName}'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context);
        return;
      }

      // Update collection with new outfit
      var collectionHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      // Create a new list with the current outfit added
      List<String> updatedOutfitPointers = [
        ...collection.outfitPointers,
        outfit.outfitId
      ];

      var collectionRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/${collection.outfitCollectionId}?userId=$userId'));

      collectionRequest.body = jsonEncode({
        "outfitCollectionName": collection.outfitCollectionName,
        "outfitPointers": updatedOutfitPointers
      });

      collectionRequest.headers.addAll(collectionHeaders);

      http.StreamedResponse collectionResponse = await collectionRequest.send();

      // Update outfit status
      var outfitHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var outfitRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/outfits/${outfit.outfitId}?userId=$userId'));

      outfitRequest.body = jsonEncode({
        "status": "WISHLIST",
        "outfitCollectionId": collection.outfitCollectionId
      });

      outfitRequest.headers.addAll(outfitHeaders);

      http.StreamedResponse outfitResponse = await outfitRequest.send();

      if (collectionResponse.statusCode == 200 &&
          outfitResponse.statusCode == 200) {
        String collectionResponseBody =
            await collectionResponse.stream.bytesToString();
        String outfitResponseBody = await outfitResponse.stream.bytesToString();
        print("Collection updated successfully: $collectionResponseBody");
        print("Outfit updated successfully: $outfitResponseBody");

        // Refresh collections list
        await fetchCollections();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Outfit saved to ${collection.outfitCollectionName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error updating collection: ${collectionResponse.reasonPhrase}");
        print("Error updating outfit: ${outfitResponse.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save outfit to collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
      Navigator.pop(context);
    } catch (e) {
      print("Exception when saving outfit to collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
      Navigator.pop(context);
    }
  }

@override
  void dispose() {
    _midnightTimer?.cancel();
    ReportManager().dispose();
    newCollectionController.dispose();
    super.dispose();
  }

  Future<void> fetchApparelDetails(Outfit outfit) async {
  try {
    String? authToken = await SharedPrefsService.getAuthToken();
    String? userId = await SharedPrefsService.getUserId();

    if (authToken == null || userId == null) {
      print("Auth token or userId is null");
      return;
    }

    // For better performance, only fetch the first four apparels
    for (int i = 0; i < outfit.apparelPointers.length && i < 4; i++) {
      if (outfit.apparelPointers[i].isEmpty) {
        continue;
      }

      var headers = {'Authorization': 'Bearer $authToken'};

      // Modified URL to include userId as query parameter
      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.apparelApi}apparel/${outfit.apparelPointers[i]}?userId=$userId'));
      request.headers.addAll(headers);

      try {
        http.StreamedResponse response = await request.send();

        if (response.statusCode == 200) {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

          if (jsonResponse['data'] != null) {
            Apparel apparel = Apparel.fromJson(jsonResponse);
            outfit.apparels.add(apparel);
          }
        } else {
          print("Info: Apparel ${outfit.apparelPointers[i]} not found or not accessible: ${response.reasonPhrase}");
        }
      } catch (e) {
        print("Info: Exception fetching apparel ${outfit.apparelPointers[i]}: $e");
        // Continue to next apparel even if one fails
      }
    }
  } catch (e) {
    print("Exception when fetching apparel details: $e");
  }
}

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Nova Recommends',
          style: TextStyle(
            fontFamily: "SatoshiB",
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Outfit suggestions from us, for you via our magical formula.',
          style: TextStyle(
            fontFamily: "SatoshiM",
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 485,
          child: isLoading
              ? _buildShimmerOutfitCard()
              : outfits.isEmpty
                  ? _buildNoCardsWidget()
                  : PageView.builder(
                      onPageChanged: (index) {
                        setState(() {
                          _currentOutfitIndex = index;
                        });
                      },
                      itemCount: outfits.length + 1,
                      itemBuilder: (context, index) {
                        if (index < outfits.length) {
                          return buildOutfitCard(context, outfits[index]);
                        } else {
                          return buildEmptyOutfitCard(context);
                        }
                      },
                    ),
        ),
        // Indicator dots
        Center(
          child: Container(
            margin: const EdgeInsets.only(top: 12, bottom: 12),
            child: isLoading || outfits.isEmpty
                ? const SizedBox(
                    height: 8) // Hide dots when loading or no outfits
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      outfits.length + 1, // Add 1 for the empty state card
                      (index) => _buildIndicator(index == _currentOutfitIndex),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoCardsWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        child: Container(
          height: 502,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/emptyImages/almirah.png', // Make sure this matches your asset path
                width: 300,
                height: 300,
              ),
              const SizedBox(height: 12),
              const Text(
                "Your wardrobe is empty!",
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  "Add 6 pieces to start—2 tops, \n2 bottoms & 2 shoes.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerOutfitCard() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: LinearProgressIndicator(
              value: null, // Indeterminate progress
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF76037)),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icons/star.png', // Using star from assets
                  width: 14,
                  height: 14,
                ),
                const SizedBox(width: 8),
                Text(
                  'Understanding your style...',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator(bool isActive) {
    return Container(
      width: isActive ? 20 : 8,
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFF9E9E9E) : const Color(0xFFD6D6D6),
        borderRadius: BorderRadius.circular(10),
      ),
    );
  }

  Widget buildOutfitCard(BuildContext context, Outfit outfit) {
    return GestureDetector(
      onTap: () {
        // Create a new Outfit object of the expected type
        final detailOutfit = outfit_screen.Outfit(
          userId: outfit.userId,
          outfitId: outfit.outfitId,
          outfitName: outfit.outfitName,
          apparelPointers: outfit.apparelPointers,
          dateCreated: outfit.dateCreated,
          status: outfit.status,
          tags: outfit.tags,
          description: outfit.description,
        );

        // Copy over the apparels
        for (var apparel in outfit.apparels) {
          // You may need to convert the apparel objects too if they have different types
          detailOutfit.apparels.add(outfit_screen.Apparel(
            apparelId: apparel.apparelId,
            apparelType: apparel.apparelType,
            apparelMediaUrl: apparel.apparelMediaUrl,
            productName: apparel.productName,
            brand: apparel.brand,
            colour: apparel.colour,
          ));
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                NewOfficeWearDetailsScreen(outfit: detailOutfit),
          ),
        );
      },
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xffD9D9D9)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and score
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      outfit.outfitName,
                      style: const TextStyle(
                        fontFamily: "SatoshiR",
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                    height: 30,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Image grid with adaptive layout based on number of images
              if (outfit.apparels.isEmpty)
                Container(
                  height: 245,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: Icon(Icons.broken_image, size: 48),
                )
              else if (outfit.apparels.length == 1)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    outfit.apparels[0].apparelMediaUrl,
                    height: 245,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 245,
                      color: Colors.grey[300],
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                )
              else if (outfit.apparels.length == 2)
                Column(
                  children: [
                    // Top image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        outfit.apparels[0].apparelMediaUrl,
                        height: 120,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Icon(Icons.broken_image),
                        ),
                      ),
                    ),
                    // Horizontal divider
                    Container(
                      height: 1,
                      color: const Color(0xFFE0E0E0),
                      margin: const EdgeInsets.symmetric(vertical: 4),
                    ),
                    // Bottom image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        outfit.apparels[1].apparelMediaUrl,
                        height: 120,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Icon(Icons.broken_image),
                        ),
                      ),
                    ),
                  ],
                )
              else if (outfit.apparels.length == 3)
                Column(
                  children: [
                    // Top image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        outfit.apparels[0].apparelMediaUrl,
                        height: 120,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Icon(Icons.broken_image),
                        ),
                      ),
                    ),
                    // Horizontal divider
                    Container(
                      height: 1,
                      color: const Color(0xFFE0E0E0),
                      margin: const EdgeInsets.symmetric(vertical: 4),
                    ),
                    // Bottom row with two images
                    Row(
                      children: [
                        // Left image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[1].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                        // Vertical divider
                        Container(
                          height: 120,
                          width: 1,
                          color: const Color(0xFFE0E0E0),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                        ),
                        // Right image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[2].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              else
                Column(
                  children: [
                    Row(
                      children: [
                        // Left image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[0].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                        // Vertical divider
                        Container(
                          height: 120,
                          width: 1,
                          color: const Color(0xFFE0E0E0),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                        ),
                        // Right image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[1].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Horizontal divider
                    Container(
                      height: 1,
                      color: const Color(0xFFE0E0E0),
                      margin: const EdgeInsets.symmetric(vertical: 4),
                    ),
                    Row(
                      children: [
                        // Left image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[2].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                        // Vertical divider
                        Container(
                          height: 120,
                          width: 1,
                          color: const Color(0xFFE0E0E0),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                        ),
                        // Right image
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              outfit.apparels[3].apparelMediaUrl,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                height: 120,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

              const SizedBox(height: 12),
              // Action buttons
              Row(
                children: [
                  IconButton(
                    icon: const Icon(FeatherIcons.thumbsUp,
                        size: 20, color: Color(0xff8F8F8F)),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.thumbsDown,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.send,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.bookmark,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {
                      _showSaveOutfitBottomSheet(context, outfit);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Description
              Text(
                outfit.description,
                style: const TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 10),
              // Tags
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _buildColorfulTags(outfit.tags),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Add this method to your HandpickedSection class
  Widget buildEmptyOutfitCard(BuildContext context) {
    // Get gender-based image path
    final String imagePath;
    if (_userGender == null) {
      imagePath = 'assets/cardImages/women_recomm.png'; // Default image
    } else if (_userGender == 'MASCULINE') {
      imagePath = 'assets/cardImages/men_recomm.png';
    } else {
      imagePath = 'assets/cardImages/women_recomm.png';
    }

    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              imagePath, // Gender-based image
              height: 120,
              width: 120,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 24),
            const Text(
              'Fresh New Fits Everyday!',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              "That's it for today!",
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Come tomorrow to see some\nfresh new fits.',
              style: TextStyle(
                fontFamily: "SatoshiR",
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build colorful tags
  List<Widget> _buildColorfulTags(List<String> tags) {
    // Map of tag colors
    final Map<String, Color> tagColors = {
      'Casual': const Color(0xFFFCE4EC),
      'Night Out': const Color(0xFFFFF8E1),
      'Date Night': const Color(0xFFF1F8E9),
      'Workwear': const Color(0xFFE0F7FA),
      'Semi-formal': const Color(0xFFE8EAF6),
      'Trendy': const Color(0xFFF3E5F5),
      'Office': const Color(0xFFE0F2F1),
      'Formal': const Color(0xFFE3F2FD),
      'Classic': const Color(0xFFE8F5E9),
      'Weekend': const Color(0xFFEFEBE9),
    };

    return tags.map((tag) {
      // Use the predefined color or default to a light gray
      final Color tagColor = tagColors[tag] ?? const Color(0xFFEEEEEE);

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tagColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          tag,
          style: const TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 12,
            color: Colors.black87,
          ),
        ),
      );
    }).toList();
  }
}
