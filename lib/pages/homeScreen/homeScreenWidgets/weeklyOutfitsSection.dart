import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/newOutfitScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart'
    as outfit_screen;
import 'package:monova_ai_stylist/pages/features/wadrobe/search/outfitsScreen.dart';
// import 'package:monova_ai_stylist/services/outfits/outfit_service.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/services/weatherservice.dart';
import 'package:shimmer/shimmer.dart';

class WeeklyOutfitsSection extends StatefulWidget {
  @override
  State<WeeklyOutfitsSection> createState() => WeeklyOutfitsSectionState();
}

class WeeklyOutfitsSectionState extends State<WeeklyOutfitsSection> {
  List<OutfitCollection> collections = [];
  bool isCollectionLoading = false;
  TextEditingController newCollectionController = TextEditingController();
  Map<String, dynamic>? currentOutfit;
  String _selectedOption = 'Workwear';
  late Box weeklyOutfitsBox;
  final String weeklyOutfitsBoxName = 'weekly_outfits';
  final String lastWeeklyFetchKey = 'last_weekly_fetch_time';
  final Duration cacheValidDuration = Duration(hours: 24);
  final List<String> _options = [
    'Workwear',
    'Semi-formal',
    'Trendy',
    'Casual',
    'Night Out',
    'Date Night'
  ];
  bool isLoading = true;
  Map<String, Map<String, dynamic>> weeklyOutfits = {};
  List<String> weekdays = [];
  Map<String, List<Map<String, dynamic>>> formattedOutfits = {};

  @override
  void initState() {
    super.initState();
    _initHive();
  }

  Future<void> _initHive() async {
    try {
      // Initialize Hive
      if (!Hive.isBoxOpen(weeklyOutfitsBoxName)) {
        await Hive.openBox(weeklyOutfitsBoxName);
      }
      weeklyOutfitsBox = Hive.box(weeklyOutfitsBoxName);

      // Load data from cache or network
      await loadWeeklyOutfits();
    } catch (e) {
      print("Error initializing Hive for weekly outfits: $e");
      // Fall back to network fetch if Hive initialization fails
      fetchWeeklyOutfits();
    }
  }

  Future<void> loadWeeklyOutfits() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Check if we have cached data and if it's still valid
      if (weeklyOutfitsBox.containsKey('weekly_outfits_data') &&
          weeklyOutfitsBox.containsKey(lastWeeklyFetchKey)) {
        final lastFetch =
            DateTime.parse(weeklyOutfitsBox.get(lastWeeklyFetchKey));
        final now = DateTime.now();

        // If cache is still valid, use it
        if (now.difference(lastFetch) < cacheValidDuration) {
          final cachedData = weeklyOutfitsBox.get('weekly_outfits_data');

          // Process weekdays - ensure proper type conversion
          List<dynamic> rawWeekdays = cachedData['weekdays'] ?? [];
          List<String> cachedWeekdays =
              rawWeekdays.map((day) => day.toString()).toList();

          // Check if we need to update the cached days to match today
          DateTime today = DateTime.now();
          String todayName = DateFormat('EEEE').format(today);

          // If today's day isn't the first in our cached days, we need to refresh
          if (cachedWeekdays.isEmpty || cachedWeekdays[0] != todayName) {
            // Cache is outdated (days have changed), fetch new data
            await fetchWeeklyOutfits();
            return;
          }

          // Process formatted outfits - with proper type handling
          Map<dynamic, dynamic> rawFormattedOutfitsMap =
              cachedData['formattedOutfits'] ?? {};
          Map<String, List<Map<String, dynamic>>> processedFormattedOutfits =
              {};

          rawFormattedOutfitsMap.forEach((dayKey, outfitsList) {
            String day = dayKey.toString();
            if (outfitsList is List) {
              List<Map<String, dynamic>> typedOutfits = [];

              for (var rawOutfit in outfitsList) {
                if (rawOutfit is Map) {
                  // Convert to the expected type with safe conversions
                  Map<String, dynamic> typedOutfit = {};

                  rawOutfit.forEach((key, value) {
                    String typedKey = key.toString();

                    // Handle special cases for lists
                    if (typedKey == 'apparelPointers' || typedKey == 'tags') {
                      if (value is List) {
                        typedOutfit[typedKey] =
                            value.map((item) => item.toString()).toList();
                      } else {
                        typedOutfit[typedKey] = <String>[];
                      }
                    }
                    // Handle nested maps like apparels
                    else if (typedKey == 'apparels' && value is List) {
                      List<Map<String, dynamic>> typedApparels = [];

                      for (var apparel in value) {
                        if (apparel is Map) {
                          Map<String, dynamic> typedApparel = {};
                          apparel.forEach((apparelKey, apparelValue) {
                            typedApparel[apparelKey.toString()] = apparelValue;
                          });
                          typedApparels.add(typedApparel);
                        }
                      }

                      typedOutfit[typedKey] = typedApparels;
                    }
                    // Handle regular fields
                    else {
                      typedOutfit[typedKey] = value;
                    }
                  });

                  typedOutfits.add(typedOutfit);
                }
              }

              processedFormattedOutfits[day] = typedOutfits;
            }
          });

          setState(() {
            weekdays = cachedWeekdays;
            formattedOutfits = processedFormattedOutfits;
            isLoading = false;
          });

          print("Using cached weekly outfits data");

          // Load apparel details for outfits that need it
          for (var day in processedFormattedOutfits.keys) {
            for (var outfit in processedFormattedOutfits[day] ?? []) {
              if (outfit != null &&
                  (outfit['apparels'] == null || outfit['apparels'].isEmpty) &&
                  outfit['apparelPointers'] != null) {
                fetchApparelDetails(outfit);
              }
            }
          }

          // Optionally fetch latest data in background for next time
          _fetchAndCacheInBackground();
          return;
        }
      }

      // If no valid cache, fetch from network
      await fetchWeeklyOutfits();
    } catch (e) {
      print("Error loading weekly outfits data: $e");
      setState(() {
        isLoading = false;
      });

      // Always fetch from network if there's an error with the cache
      await fetchWeeklyOutfits();
    }
  }

  Future<void> _fetchAndCacheInBackground() async {
    // Fetch in background without loading indicators
    try {
      await fetchWeeklyOutfits(showLoading: false);
    } catch (e) {
      print("Background fetch of weekly outfits failed: $e");
    }
  }

  Future<void> fetchCollections() async {
    setState(() {
      isCollectionLoading = true;
    });

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isCollectionLoading = false;
        });
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/list?userId=$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        List<OutfitCollection> fetchedCollections = [];
        if (jsonResponse['data'] != null) {
          for (var collectionJson in jsonResponse['data']) {
            OutfitCollection collection =
                OutfitCollection.fromJson(collectionJson);
            fetchedCollections.add(collection);
          }
        }

        setState(() {
          collections = fetchedCollections;
          isCollectionLoading = false;
        });
      } else {
        print("Error fetching collections: ${response.reasonPhrase}");
        setState(() {
          isCollectionLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching collections: $e");
      setState(() {
        isCollectionLoading = false;
      });
    }
  }

  Future<void> createCollection(String collectionName,
      [Map<String, dynamic>? outfit]) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'POST',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections?userId=$userId'));

      // Create the request body based on whether an outfit is provided
      var requestBody = {
        "outfitCollectionName": collectionName,
        "outfitPointers": outfit != null ? [outfit['outfitId']] : []
      };

      request.body = jsonEncode(requestBody);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 201) {
        String responseBody = await response.stream.bytesToString();
        print("Collection created successfully: $responseBody");

        // Refresh collections list
        await fetchCollections();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Collection created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error creating collection: ${response.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print("Exception when creating collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> saveOutfitToCollection(
      OutfitCollection collection, Map<String, dynamic> outfit) async {
    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        return;
      }

      // Check if outfit is already in the collection
      if (collection.outfitPointers.contains(outfit['outfitId'])) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Outfit already in ${collection.outfitCollectionName}'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context);
        return;
      }

      // Update collection with new outfit
      var collectionHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      // Create a new list with the current outfit added
      List<String> updatedOutfitPointers = [
        ...collection.outfitPointers,
        outfit['outfitId']
      ];

      var collectionRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/${collection.outfitCollectionId}?userId=$userId'));

      collectionRequest.body = jsonEncode({
        "outfitCollectionName": collection.outfitCollectionName,
        "outfitPointers": updatedOutfitPointers
      });

      collectionRequest.headers.addAll(collectionHeaders);

      http.StreamedResponse collectionResponse = await collectionRequest.send();

      // Update outfit status
      var outfitHeaders = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var outfitRequest = http.Request(
          'PUT',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/outfits/${outfit['outfitId']}?userId=$userId'));

      outfitRequest.body = jsonEncode({
        "status": "WISHLIST",
        "outfitCollectionId": collection.outfitCollectionId
      });

      outfitRequest.headers.addAll(outfitHeaders);

      http.StreamedResponse outfitResponse = await outfitRequest.send();

      if (collectionResponse.statusCode == 200 &&
          outfitResponse.statusCode == 200) {
        String collectionResponseBody =
            await collectionResponse.stream.bytesToString();
        String outfitResponseBody = await outfitResponse.stream.bytesToString();
        print("Collection updated successfully: $collectionResponseBody");
        print("Outfit updated successfully: $outfitResponseBody");

        // Refresh collections list
        await fetchCollections();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Outfit saved to ${collection.outfitCollectionName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print("Error updating collection: ${collectionResponse.reasonPhrase}");
        print("Error updating outfit: ${outfitResponse.reasonPhrase}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save outfit to collection'),
            backgroundColor: Colors.red,
          ),
        );
      }
      Navigator.pop(context);
    } catch (e) {
      print("Exception when saving outfit to collection: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred'),
          backgroundColor: Colors.red,
        ),
      );
      Navigator.pop(context);
    }
  }

  void _showSaveOutfitBottomSheet(BuildContext context,
      [Map<String, dynamic>? outfit]) {
    // Store the current outfit to use in API calls
    currentOutfit = outfit;

    // If no outfit is available, show an error
    if (currentOutfit == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No outfit to save'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Fetch collections before showing the bottom sheet
    fetchCollections().then((_) {
      showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Save to',
                        style: TextStyle(
                          fontFamily: 'SatoshiM',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 24),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: isCollectionLoading
                        ? const SizedBox()
                        : SingleChildScrollView(
                            child: Column(
                              children: [
                                _buildSaveOptionRow(
                                  title: 'New Style Group',
                                  outfitCount: 0,
                                  isNew: true,
                                  onTap: () {
                                    _showNewStyleGroupModal(context);
                                  },
                                ),
                                if (collections.isNotEmpty)
                                  ...collections.map((collection) {
                                    return _buildSaveOptionRow(
                                      title: collection.outfitCollectionName,
                                      outfitCount:
                                          collection.outfitPointers.length,
                                      collection: collection,
                                      onTap: () {
                                        saveOutfitToCollection(
                                            collection, currentOutfit!);
                                      },
                                    );
                                  }).toList(),
                              ],
                            ),
                          ),
                  )
                ],
              ),
            );
          });
        },
      );
    });
  }

  Widget _buildSaveOptionRow({
    required String title,
    required int outfitCount,
    required VoidCallback onTap,
    bool isNew = false,
    OutfitCollection? collection,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 83,
            height: 83,
            decoration: BoxDecoration(
              color: isNew ? Colors.white : _getColorForCollection(title),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: isNew
                  ? const Icon(Icons.add, color: Colors.black, size: 24)
                  : (collection != null &&
                          collection.outfitCollectionMediaUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Image.network(
                            collection.outfitCollectionMediaUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              _getIconForCollection(title),
                              size: 24,
                              color: Colors.black,
                            ),
                          ),
                        )
                      : Icon(_getIconForCollection(title),
                          size: 24, color: Colors.black)),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$outfitCount outfits',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            backgroundColor: Colors.grey[200],
            child: IconButton(
              icon: const Icon(Icons.add, color: Colors.grey),
              onPressed: onTap,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return Icons.work_outline;
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return Icons.favorite;
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return Icons.wb_sunny_outlined;
    }
    return Icons.style;
  }

  Color _getColorForCollection(String collectionName) {
    if (collectionName.toLowerCase().contains('office') ||
        collectionName.toLowerCase().contains('work')) {
      return const Color(0xFFFFF8E7);
    } else if (collectionName.toLowerCase().contains('date') ||
        collectionName.toLowerCase().contains('love')) {
      return const Color(0xFFFFF1F1);
    } else if (collectionName.toLowerCase().contains('summer') ||
        collectionName.toLowerCase().contains('sun')) {
      return const Color(0xFFF5F1FF);
    }
    return Colors.white;
  }

  void _showNewStyleGroupModal(BuildContext context) {
    TextEditingController localController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'New Style Group',
                      style: TextStyle(
                        fontFamily: 'SatoshiM',
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 24),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  height: 200,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.help_outline,
                            color: Colors.grey,
                            size: 50,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'The image is auto generated\nbased on the name',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Journal Name',
                  style: TextStyle(
                    fontFamily: 'SatoshiR',
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: localController,
                  decoration: InputDecoration(
                    hintText: 'Party at Night, Fun day out...',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (localController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Please enter a name for your style group'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // Store the value before closing modal
                      final collectionName = localController.text.trim();

                      // Close this modal
                      Navigator.pop(context);

                      // Create collection WITHOUT requiring an outfit
                      createCollection(collectionName);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xffF76037),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    newCollectionController.dispose();
    super.dispose();
  }

  Future<void> fetchWeeklyOutfits({bool showLoading = true}) async {
    if (showLoading) {
      setState(() {
        isLoading = true;
      });
    }

    try {
      String? authToken = await SharedPrefsService.getAuthToken();
      String? userId = await SharedPrefsService.getUserId();

      // Get weather data to pass to the API
      final weatherData = await WeatherLocationService.getWeatherData();
      String weatherCondition = weatherData?.condition ?? "Clear";

      if (authToken == null || userId == null) {
        print("Auth token or userId is null");
        setState(() {
          isLoading = false;
        });
        return;
      }

      var headers = {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      };

      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.outfitApi}outfit/collections/get-weekly-outfits?userId=$userId&weather=$weatherCondition'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

        if (jsonResponse['data'] != null &&
            jsonResponse['data']['outfits'] != null) {
          Map<String, dynamic> outfitsData = jsonResponse['data']['outfits'];

          // Format data for display
          Map<String, List<Map<String, dynamic>>> formatted = {};
          List<String> days = [];

          // Get current date
          DateTime now = DateTime.now();

          // Create a list of 7 dates (today + next 6 days)
          List<DateTime> nextSevenDays = [];
          for (int i = 0; i < 7; i++) {
            nextSevenDays.add(now.add(Duration(days: i)));
          }

          // Process outfits for the next 7 days only
          for (DateTime date in nextSevenDays) {
            String formattedDate = DateFormat('yyyy-MM-dd').format(date);
            String dayName = DateFormat('EEEE').format(date);

            // Add the day name to our list
            days.add(dayName);

            // Check if we have outfits for this date
            if (outfitsData.containsKey(formattedDate)) {
              Map<String, dynamic> dayOutfits =
                  outfitsData[formattedDate] ?? {};
              List<Map<String, dynamic>> outfitsList = [];

              // Convert each outfit type to a list item
              dayOutfits.forEach((category, outfit) {
                if (outfit != null && outfit is Map<String, dynamic>) {
                  try {
                    outfitsList.add({
                      'category': category,
                      'outfitId': outfit['outfitId'] ?? '',
                      'outfitName': outfit['outfitName'] ?? 'Outfit',
                      'apparelPointers':
                          List<String>.from(outfit['apparelPointers'] ?? []),
                      'tags': List<String>.from(outfit['tags'] ?? []),
                      'userId': outfit['userId'] ?? '',
                      'dateCreated': outfit['dateCreated'] ??
                          DateTime.now().toIso8601String(),
                      'status': outfit['status'] ?? 'PRIMARY',
                    });
                  } catch (e) {
                    print("Error parsing outfit data: $e");
                  }
                }
              });

              formatted[dayName] = outfitsList;
            } else {
              // If no outfits exist for this date, set an empty list
              formatted[dayName] = [];
            }
          }

          // Prepare data for caching
          Map<String, dynamic> cacheData = {
            'weekdays': days,
            'formattedOutfits': formatted,
          };

          // Cache the data
          await weeklyOutfitsBox.put('weekly_outfits_data', cacheData);
          await weeklyOutfitsBox.put(
              lastWeeklyFetchKey, DateTime.now().toIso8601String());

          setState(() {
            weeklyOutfits = outfitsData.cast<String, Map<String, dynamic>>();
            weekdays = days;
            formattedOutfits = formatted;
            isLoading = false;
          });

          // Fetch apparel details for all outfits
          for (var day in formatted.keys) {
            for (var outfit in formatted[day] ?? []) {
              if (outfit != null) {
                fetchApparelDetails(outfit);
              }
            }
          }
        } else {
          print("No valid outfits data in response");
          setState(() {
            isLoading = false;
          });
        }
      } else {
        print("Error fetching weekly outfits: ${response.reasonPhrase}");
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print("Exception when fetching weekly outfits: $e");
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> fetchApparelDetails(Map<String, dynamic> outfit) async {
  if (outfit == null ||
      outfit['apparelPointers'] == null ||
      !(outfit['apparelPointers'] is List) ||
      (outfit['apparelPointers'] as List).isEmpty) {
    return;
  }

  try {
    String? authToken = await SharedPrefsService.getAuthToken();
    String? userId = await SharedPrefsService.getUserId();
    String? outfitId = outfit['outfitId'];

    if (authToken == null || userId == null) {
      print("Auth token or userId is null");
      return;
    }

    // Check for cached apparel data first
    if (outfitId != null && outfitId.isNotEmpty) {
      final String apparelCacheKey = 'weekly_apparel_${outfitId}';

      if (weeklyOutfitsBox.containsKey(apparelCacheKey)) {
        final cachedApparels = weeklyOutfitsBox.get(apparelCacheKey);
        if (cachedApparels != null && cachedApparels is List) {
          List<Map<String, dynamic>> typedApparels = [];

          for (var apparel in cachedApparels) {
            if (apparel is Map) {
              // Convert to the expected type with safe conversions
              Map<String, dynamic> typedApparel = {};

              apparel.forEach((key, value) {
                String typedKey = key.toString();
                typedApparel[typedKey] = value;
              });

              typedApparels.add(typedApparel);
            }
          }

          if (mounted) {
            setState(() {
              outfit['apparels'] = typedApparels;
            });
          }
          return;
        }
      }
    }

    List<Map<String, dynamic>> apparels = [];
    List<String> validApparelIds = [];

    // For better performance, only fetch the first four apparels
    List<String> apparelIds = List<String>.from(outfit['apparelPointers']);
    
    for (int i = 0; i < apparelIds.length && i < 4; i++) {
      if (apparelIds[i] == null || apparelIds[i].isEmpty) {
        continue;
      }

      var headers = {'Authorization': 'Bearer $authToken'};

      // Modified URL to include userId as query parameter
      var request = http.Request(
          'GET',
          Uri.parse(
              '${ApiConstants.apparelApi}apparel/${apparelIds[i]}?userId=$userId'));
      request.headers.addAll(headers);

      try {
        http.StreamedResponse response = await request.send();

        if (response.statusCode == 200) {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

          if (jsonResponse['data'] != null) {
            // Add the valid apparel to our list
            Map<String, dynamic> apparel = {
              'apparelId': jsonResponse['data']['apparelId'] ?? '',
              'apparelType': jsonResponse['data']['apparelType'] ?? '',
              'apparelMediaUrl':
                  jsonResponse['data']['apparelMediaUrl'] ?? '',
              'productName': jsonResponse['data']['productName'] ?? '',
              'brand': jsonResponse['data']['brand'] ?? '',
              'colour': jsonResponse['data']['colour'] ?? '',
            };
            apparels.add(apparel);
            validApparelIds.add(apparelIds[i]); // Keep track of valid IDs
          }
        } else {
          print(
              "Info: Apparel ${apparelIds[i]} not found or not accessible: ${response.reasonPhrase}");
        }
      } catch (e) {
        print("Info: Exception fetching apparel ${apparelIds[i]}: $e");
      }
    }

    // Only update the outfit if we found at least one valid apparel
    if (apparels.isNotEmpty) {
      if (mounted) {
        setState(() {
          outfit['apparels'] = apparels;
          // Optionally update the apparel pointers to only include valid ones
          outfit['apparelPointers'] = validApparelIds;
        });
      }

      // Cache the apparel data if we have an outfitId
      if (outfitId != null && outfitId.isNotEmpty) {
        final String apparelCacheKey = 'weekly_apparel_${outfitId}';
        await weeklyOutfitsBox.put(apparelCacheKey, apparels);
      }
    } else {
      // If we couldn't find any valid apparels, mark this outfit
      if (mounted) {
        setState(() {
          outfit['apparels'] = [];
          outfit['apparelUnavailable'] = true; // Flag for UI to handle
        });
      }
    }
  } catch (e) {
    print("Exception when fetching apparel details: $e");
    // Ensure we set an empty apparels list to prevent repeated fetching attempts
    if (mounted) {
      setState(() {
        outfit['apparels'] = [];
      });
    }
  }
}

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Weekly Outfits',
          style: TextStyle(
            fontFamily: "SatoshiB",
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Let Nova be your style guru for the week!',
          style: TextStyle(
            fontFamily: "SatoshiM",
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 450,
          child: isLoading
              ? _buildShimmerOutfitCards()
              : weekdays.isEmpty
                  ? _buildNoCardsWidget()
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: weekdays.length,
                      itemBuilder: (context, index) {
                        String day = weekdays[index];
                        List<Map<String, dynamic>> outfits =
                            formattedOutfits[day] ?? [];

                        // Default to first outfit if there's at least one
                        Map<String, dynamic>? selectedOutfit =
                            outfits.isNotEmpty ? outfits.first : null;

                        if (selectedOutfit == null) {
                          return Container(); // Skip this day if no outfits
                        }

                        return _buildOutfitCard(
                          day: day,
                          outfit: selectedOutfit,
                          isFirst: index == 0,
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildShimmerOutfitCards() {
    return Container(
      height: 290,
      width: MediaQuery.of(context).size.width * 0.85,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffD9D9D9)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: LinearProgressIndicator(
              value: null, // Indeterminate progress
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFF76037)),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icons/star.png', // Using star from assets
                  width: 14,
                  height: 14,
                ),
                const SizedBox(width: 8),
                Text(
                  'Understanding your style...',
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoCardsWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        child: Container(
          height: 450,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/emptyImages/almirah.png',
                width: 200,
                height: 200,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 200,
                  height: 200,
                  color: Colors.grey[200],
                  child: Icon(Icons.broken_image,
                      size: 50, color: Colors.grey[400]),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                "No weekly outfits available!",
                style: TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  "Check back later for your weekly outfit suggestions.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: "SatoshiR",
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _fetchOutfitForDayAndVibe(String day, String vibe) async {
  try {
    print("==== VIBE CHANGED: $day to $vibe ====");
    
    // First, determine which date corresponds to the selected day
    DateTime now = DateTime.now();
    int dayIndex = weekdays.indexOf(day);
    if (dayIndex == -1) {
      print("Invalid day selected: $day");
      return;
    }
    
    // Get the date string corresponding to the selected day from our data
    List<String> sortedDates = weeklyOutfits.keys.toList()..sort();
    if (dayIndex >= sortedDates.length) {
      print("No data available for selected day: $day");
      return;
    }
    String formattedDate = sortedDates[dayIndex];
    print("Date mapped from $day to $formattedDate");
    
    // Show loading indicator for the specific day
    setState(() {
      if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
        formattedOutfits[day]![0]['isLoading'] = true;
      } else {
        // Create a placeholder outfit with loading state if none exists
        formattedOutfits[day] = [{
          'category': vibe.toLowerCase(),
          'outfitId': '',
          'outfitName': 'Loading Outfit...',
          'apparelPointers': <String>[],
          'tags': <String>[],
          'userId': '',
          'dateCreated': DateTime.now().toIso8601String(),
          'status': 'PRIMARY',
          'isLoading': true,
        }];
      }
    });

    // Check if we already have an outfit for this date and vibe in our data
    if (weeklyOutfits.containsKey(formattedDate) && 
        weeklyOutfits[formattedDate]!.containsKey(vibe.toLowerCase())) {
      
      print("Found cached outfit for $day with vibe ${vibe.toLowerCase()}");
      
      // Get the outfit data for this vibe
      Map<String, dynamic> outfitData = weeklyOutfits[formattedDate]![vibe.toLowerCase()];
      print("Using outfit: ${outfitData['outfitId']} - ${outfitData['outfitName']}");
      
      // Create formatted outfit data
      Map<String, dynamic> newOutfit = {
        'category': vibe.toLowerCase(),
        'outfitId': outfitData['outfitId'] ?? '',
        'outfitName': outfitData['outfitName'] ?? 'Outfit',
        'apparelPointers': outfitData['apparelPointers'] != null
            ? List<String>.from(outfitData['apparelPointers'])
            : [],
        'tags': outfitData['tags'] != null
            ? List<String>.from(outfitData['tags'])
            : [],
        'userId': outfitData['userId'] ?? '',
        'dateCreated':
            outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
        'status': outfitData['status'] ?? 'PRIMARY',
        'isLoading': true, // Set loading state while fetching apparels
      };

      // Update the outfit in formattedOutfits
      setState(() {
        if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
          formattedOutfits[day]![0] = newOutfit;
        } else {
          formattedOutfits[day] = [newOutfit];
        }
      });

      // Fetch apparel details for the new outfit
      print("Fetching apparel details for outfit ${newOutfit['outfitId']}");
      await fetchApparelDetails(newOutfit);
      
      // Set loading to false after fetching apparels
      setState(() {
        if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
          formattedOutfits[day]![0]['isLoading'] = false;
        }
      });
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Updated to $vibe style'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 1),
        ),
      );
      return;
    }
    
    // If we don't have the outfit in our local data, fetch from API
    String? authToken = await SharedPrefsService.getAuthToken();
    String? userId = await SharedPrefsService.getUserId();

    // Get weather data
    final weatherData = await WeatherLocationService.getWeatherData();
    String weatherCondition = weatherData?.condition ?? "Clear";

    if (authToken == null || userId == null) {
      print("Auth token or userId is null");
      return;
    }

    var headers = {
      'Authorization': 'Bearer $authToken',
      'Content-Type': 'application/json'
    };

    String apiUrl = '${ApiConstants.outfitApi}outfit/collections/get-day-vibe-outfit?weather=$weatherCondition&day=$formattedDate&vibe=$vibe&userId=$userId';
    print("API REQUEST: GET $apiUrl");

    var request = http.Request('GET', Uri.parse(apiUrl));
    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    print("API RESPONSE STATUS: ${response.statusCode}");

    if (response.statusCode == 200) {
      String responseBody = await response.stream.bytesToString();
      print("API RESPONSE: $responseBody");
      Map<String, dynamic> jsonResponse = jsonDecode(responseBody);

      if (jsonResponse['data'] != null) {
        Map<String, dynamic> outfitData = jsonResponse['data'];
        print("Outfit data received: ${outfitData['outfitId']} - ${outfitData['outfitName']}");

        // Create formatted outfit data
        Map<String, dynamic> newOutfit = {
          'category': vibe.toLowerCase(),
          'outfitId': outfitData['outfitId'] ?? '',
          'outfitName': outfitData['outfitName'] ?? 'Outfit',
          'apparelPointers': outfitData['apparelPointers'] != null
              ? List<String>.from(outfitData['apparelPointers'])
              : [],
          'tags': outfitData['tags'] != null
              ? List<String>.from(outfitData['tags'])
              : [],
          'userId': outfitData['userId'] ?? '',
          'dateCreated':
              outfitData['dateCreated'] ?? DateTime.now().toIso8601String(),
          'status': outfitData['status'] ?? 'PRIMARY',
          'isLoading': true, // Set loading state while fetching apparels
        };

        // Also update our weeklyOutfits data structure to cache this outfit
        if (!weeklyOutfits.containsKey(formattedDate)) {
          weeklyOutfits[formattedDate] = {};
        }
        weeklyOutfits[formattedDate]![vibe.toLowerCase()] = outfitData;

        // Update the outfit in formattedOutfits
        setState(() {
          if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
            formattedOutfits[day]![0] = newOutfit;
          } else {
            formattedOutfits[day] = [newOutfit];
          }
        });

        // Fetch apparel details for the new outfit
        print("Fetching apparel details for outfit ${newOutfit['outfitId']}");
        await fetchApparelDetails(newOutfit);
        
        // Set loading to false after fetching apparels
        setState(() {
          if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
            formattedOutfits[day]![0]['isLoading'] = false;
          }
        });
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Updated to $vibe style'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
          ),
        );
      } else {
        print("No data in API response");
        // Set loading to false if no data
        setState(() {
          if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
            formattedOutfits[day]![0]['isLoading'] = false;
          }
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No outfit available for $vibe style'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } else {
      print("API ERROR: ${response.reasonPhrase}");
      // Set loading to false on error
      setState(() {
        if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
          formattedOutfits[day]![0]['isLoading'] = false;
        }
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update outfit style'),
          backgroundColor: Colors.red,
        ),
      );
    }
  } catch (e) {
    print("EXCEPTION when fetching outfit for day and vibe: $e");
    print(e.toString());
    
    // Set loading to false
    setState(() {
      int dayIndex = weekdays.indexOf(day);
      if (dayIndex != -1) {
        if (formattedOutfits[day] != null && formattedOutfits[day]!.isNotEmpty) {
          formattedOutfits[day]![0]['isLoading'] = false;
        }
      }
    });
    
    // Show error message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('An error occurred loading the outfit'),
        backgroundColor: Colors.red,
      ),
    );
  }
}

  Widget _buildOutfitCard({
    required String day,
    required Map<String, dynamic> outfit,
    bool isFirst = false,
  }) {
    // Get apparel items from the outfit
    List<Map<String, dynamic>> apparels = [];
    if (outfit['apparels'] != null && outfit['apparels'] is List) {
      apparels = List<Map<String, dynamic>>.from(outfit['apparels']);
    }

    return GestureDetector(
      onTap: () {
        try {
          // Create an outfit object for the detail screen
          final detailOutfit = Outfit(
            userId: outfit['userId'] ?? '',
            outfitId: outfit['outfitId'] ?? '',
            outfitName: outfit['outfitName'] ?? 'Outfit',
            apparelPointers: outfit['apparelPointers'] != null
                ? List<String>.from(outfit['apparelPointers'])
                : [],
            dateCreated:
                outfit['dateCreated'] ?? DateTime.now().toIso8601String(),
            status: outfit['status'] ?? 'PRIMARY',
            tags:
                outfit['tags'] != null ? List<String>.from(outfit['tags']) : [],
            description:
                'Category: ${(outfit['category'] ?? 'Style').toUpperCase()}',
          );

          // Copy over the apparels
          if (apparels.isNotEmpty) {
            for (var apparel in apparels) {
              if (apparel != null) {
                detailOutfit.apparels.add(Apparel(
                  apparelId: apparel['apparelId'] ?? '',
                  apparelType: apparel['apparelType'] ?? '',
                  apparelMediaUrl: apparel['apparelMediaUrl'] ?? '',
                  productName: apparel['productName'] ?? '',
                  brand: apparel['brand'] ?? '',
                  colour: apparel['colour'] ?? '',
                ));
              }
            }
          }

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  NewOfficeWearDetailsScreen(outfit: detailOutfit),
            ),
          );
        } catch (e) {
          print("Error navigating to outfit details: $e");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not open outfit details'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Container(
        width: 336,
        margin: EdgeInsets.only(right: 16, left: isFirst ? 0 : 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFEEEEEE)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dynamic grid of clothing items based on number of apparels
            Container(
              padding: const EdgeInsets.all(16),
              child: outfit['isLoading'] == true
                  ? Center(
                      child: Container(
                        height: 290,
                        width: double.infinity,
                        color: Colors.grey[100],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFFF76037)),
                            ),
                            SizedBox(height: 16),
                            Text(
                              "Loading outfit...",
                              style: TextStyle(
                                fontFamily: "SatoshiR",
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : apparels.isEmpty
                      ? Container(
                          height: 290,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: Icon(Icons.broken_image, size: 48),
                        )
                      : apparels.length == 1
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                apparels[0]['apparelMediaUrl'] ?? '',
                                height: 290,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Container(
                                  height: 290,
                                  color: Colors.grey[300],
                                  child:
                                      const Icon(Icons.broken_image, size: 40),
                                ),
                              ),
                            )
                          : apparels.length == 2
                              ? Column(
                                  children: [
                                    // Top image
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        apparels[0]['apparelMediaUrl'] ?? '',
                                        height: 140,
                                        width: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                Container(
                                          height: 140,
                                          color: Colors.grey[300],
                                          child: const Icon(Icons.broken_image,
                                              size: 40),
                                        ),
                                      ),
                                    ),
                                    // Horizontal divider
                                    Container(
                                      height: 1,
                                      width: double.infinity,
                                      color: const Color(0xFFEEEEEE),
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 4),
                                    ),
                                    // Bottom image
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        apparels[1]['apparelMediaUrl'] ?? '',
                                        height: 140,
                                        width: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                Container(
                                          height: 140,
                                          color: Colors.grey[300],
                                          child: const Icon(Icons.broken_image,
                                              size: 40),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : apparels.length == 3
                                  ? Column(
                                      children: [
                                        // Top image
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Image.network(
                                            apparels[0]['apparelMediaUrl'] ??
                                                '',
                                            height: 140,
                                            width: double.infinity,
                                            fit: BoxFit.cover,
                                            errorBuilder:
                                                (context, error, stackTrace) =>
                                                    Container(
                                              height: 140,
                                              color: Colors.grey[300],
                                              child: const Icon(
                                                  Icons.broken_image,
                                                  size: 40),
                                            ),
                                          ),
                                        ),
                                        // Horizontal divider
                                        Container(
                                          height: 1,
                                          width: double.infinity,
                                          color: const Color(0xFFEEEEEE),
                                          margin: const EdgeInsets.symmetric(
                                              vertical: 4),
                                        ),
                                        // Bottom row with two images
                                        Row(
                                          children: [
                                            // Left image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[1]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // Vertical divider
                                            Container(
                                              height: 140,
                                              width: 1,
                                              color: const Color(0xFFEEEEEE),
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 4),
                                            ),
                                            // Right image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[2]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    )
                                  : Column(
                                      children: [
                                        // Top row (2 items)
                                        Row(
                                          children: [
                                            // Left image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[0]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // Vertical divider
                                            Container(
                                              height: 140,
                                              width: 1,
                                              color: const Color(0xFFEEEEEE),
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 4),
                                            ),
                                            // Right image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[1]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        // Horizontal divider
                                        Container(
                                          height: 1,
                                          width: double.infinity,
                                          color: const Color(0xFFEEEEEE),
                                          margin: const EdgeInsets.symmetric(
                                              vertical: 4),
                                        ),
                                        // Bottom row (2 items)
                                        Row(
                                          children: [
                                            // Left image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[2]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // Vertical divider
                                            Container(
                                              height: 140,
                                              width: 1,
                                              color: const Color(0xFFEEEEEE),
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 4),
                                            ),
                                            // Right image
                                            Expanded(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  apparels[3]
                                                          ['apparelMediaUrl'] ??
                                                      '',
                                                  height: 140,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Container(
                                                    height: 140,
                                                    color: Colors.grey[300],
                                                    child: const Icon(
                                                        Icons.broken_image,
                                                        size: 40),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
            ),

            // Day label and dropdown
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${day}'s Fit",
                    style: const TextStyle(
                      fontFamily: "SatoshiR",
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF4A4A4A),
                    ),
                  ),
                  // Modified PopupMenuButton in _buildOutfitCard function
                  Container(
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: PopupMenuButton<String>(
                      color: Colors.white,
                      offset: const Offset(0, 40),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      onSelected: (String value) async {
                        setState(() {
                          _selectedOption = value;
                        });

                        // Fetch outfit for the selected vibe
                        await _fetchOutfitForDayAndVibe(day, value);
                      },
                      itemBuilder: (BuildContext context) {
                        return [
                          PopupMenuItem<String>(
                            enabled: false,
                            padding: EdgeInsets.zero,
                            height: 100,
                            child: Container(
                              width: 250,
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "workwear");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFEAF6FF),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "workwear",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "semi-formal");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFE5E5FA),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "semi-formal",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "trendy");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFF5E5FA),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "trendy",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "casual");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFFE9E5),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "casual",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "night out");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFFF8E5),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "night out",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.pop(context, "date night");
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 12),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFEFFFF5),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Text(
                                            "date night",
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontFamily: "SatoshiM",
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF4A4A4A),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ];
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _selectedOption,
                              style: const TextStyle(
                                fontSize: 12,
                                fontFamily: "SatoshiM",
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF4A4A4A),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.keyboard_arrow_down,
                              size: 18,
                              color: Colors.grey[600],
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),

            // Action buttons
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.thumbsUp,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.thumbsDown,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.send,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.bookmark,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {
                      _showSaveOutfitBottomSheet(context, outfit);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}