import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

class OutfitDetailScreen extends StatelessWidget {
  final Map<String, dynamic> outfitData;

  const OutfitDetailScreen({
    Key? key,
    required this.outfitData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Outfit Title
              Text(
                outfitData['title'],
                style: const TextStyle(
                  fontSize: 18,
                  fontFamily: "<PERSON>shi<PERSON>",
                  fontWeight: FontWeight.w500,
                  color: Color(0xff1C1B1F),
                ),
              ),
              const SizedBox(height: 20),

              // Outfit Images Container
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F7F7),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  children: [
                    // Top image (shirt)
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Image.asset(
                        'assets/staticImages/cloth.png',
                        height: 160,
                        fit: BoxFit.contain,
                      ),
                    ),

                    // Divider line
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                      child: const Divider(height: 1, color: Color(0xFFE0E0E0)),
                    ),

                    // Bottom row with jeans and boots
                    Row(
                      children: [
                        // Left image (jeans)
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Image.asset(
                              'assets/staticImages/cloth.png',
                              height: 120,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),

                        // Vertical divider
                        Container(
                          height: 120,
                          width: 1,
                          color: const Color(0xFFE0E0E0),
                        ),

                        // Right image (boots)
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Image.asset(
                              'assets/staticImages/cloth.png',
                              height: 120,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.thumbsUp,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(
                      FeatherIcons.thumbsDown,
                      size: 20,
                      color: Color(0xff8F8F8F),
                    ),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(FeatherIcons.send),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(FeatherIcons.bookmark),
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Description
              Text(
                outfitData['description'] ,
                style: const TextStyle(
                  fontFamily: "SatoshiR",
                  fontSize: 15,
                  color: Colors.black87,
                ),
              ),

              const SizedBox(height: 12),

              // Tags
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _buildTags(outfitData['tags']),
              ),

              const SizedBox(height: 24),
              const Divider(height: 1, color: Color(0xFFE0E0E0)),
              const SizedBox(height: 24),

              // In this look section
              const Text(
                'In this look',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: "SatoshiM",
                  fontWeight: FontWeight.w500,
                  color: Color(0xff1C1B1F),
                ),
              ),

              const SizedBox(height: 16),

              // Item details list
              _buildItemCard(
                'assets/staticImages/cloth.png',
                'Black floral flame full sleeve shirt',
              ),

              const SizedBox(height: 12),

              _buildItemCard(
                'assets/staticImages/cloth.png',
                'Dark blue, wide legged, high waisted jeans',
              ),

              const SizedBox(height: 12),

              _buildItemCard(
                'assets/staticImages/cloth.png',
                'Black leather booties',
              ),

              const SizedBox(height: 40),
              const Divider(height: 1, color: Color(0xFFE0E0E0)),
              const SizedBox(height: 40),

              // Delete outfit button
              Center(
                child: Container(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 40, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(48),
                        side: const BorderSide(color: Color(0xffE7E7E7)),
                      ),
                    ),
                    onPressed: () {
                      // Handle delete outfit
                      Navigator.pop(context);
                    },
                    child: const Text(
                      'Archive Outfit',
                      style: TextStyle(
                        fontFamily: "SatoshiM",
                        fontSize: 16,
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  // Build individual tag widgets
  List<Widget> _buildTags(List<dynamic> tags) {
    final Map<String, Color> tagColors = {
      'Workwear': const Color(0xFFE0F7FA),
      'Semi-formal': const Color(0xFFE8EAF6),
      'Trendy': const Color(0xFFF3E5F5),
      'Office': const Color(0xFFE0F2F1),
      'Formal': const Color(0xFFE3F2FD),
      'Classic': const Color(0xFFE8F5E9),
      'Casual': const Color(0xFFFFF8E1),
      'Weekend': const Color(0xFFEFEBE9),
    };

    return tags.map<Widget>((tag) {
      final String tagStr = tag.toString();
      final Color tagColor = tagColors[tagStr] ?? const Color(0xFFEEEEEE);

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tagColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          tagStr,
          style: const TextStyle(
            fontFamily: "SatoshiR",
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
      );
    }).toList();
  }

  // Build individual item card
  Widget _buildItemCard(String imagePath, String itemName) {
    return Row(
      children: [
        // Item image
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              width: 1,
              color: const Color(0xFFE7E7E7),
            ),
            color: const Color(0xFFF8F7F7),
          ),
          width: 160,
          height: 160,
          padding: const EdgeInsets.all(8),
          child: Image.asset(
            imagePath,
            fit: BoxFit.cover,
          ),
        ),

        // Item name
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  itemName,
                  style: const TextStyle(
                    fontFamily: "SatoshiM",
                    fontSize: 14,
                    color: Color(0xff19191A),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
