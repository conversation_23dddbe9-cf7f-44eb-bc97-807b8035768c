import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:universal_html/html.dart' as html;
import 'package:monova_ai_stylist/widgets/bottomNavBar/bottomNavBar.dart';
class SignInLoginScreen extends StatefulWidget {
  const SignInLoginScreen({super.key});
  @override
  State<SignInLoginScreen> createState() => _SignInLoginScreenState();
}
class _SignInLoginScreenState extends State<SignInLoginScreen> {
  bool _isLoading = true;
  @override
  void initState() {
    super.initState();
    _checkForSSOLogin();
  }
  Future<void> _checkForSSOLogin() async {
    final uri = Uri.parse(html.window.location.href);
    final token = uri.queryParameters['token'];
    final subscriberId = uri.queryParameters['subscriberId'];
    if (token != null && subscriberId != null) {
      await _handleSSOLogin(token, subscriberId);
    } else {
      setState(() => _isLoading = false);
      // Optional: Show an error or fallback UI if no SSO params exist
    }
  }
  Future<void> _handleSSOLogin(String token, String subscriberId) async {
    try {
      var headers = {
        'Content-Type': 'application/json'
      };
      var request = http.Request(
        'POST',
        Uri.parse('https://s4vx2ts9w5.execute-api.us-east-1.amazonaws.com/dev/manychat/sso/verifySSOToken')
      );
      request.body = json.encode({
        "manychatSubscriberId": subscriberId,
        "token": token
      });
      request.headers.addAll(headers);
      http.StreamedResponse streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          final jwtToken = responseData['jwt'];
          final decodedToken = JwtDecoder.decode(jwtToken);
          final userId = decodedToken['userId'];
          await SharedPrefsService.saveAuthData(
            authToken: jwtToken,
            userId: userId,
            fullName: "SSO User", // Update if your API provides a name
          );
          if (!mounted) return;
          // Navigate to BottomNavScreen
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => BottomNavScreen()),
            (route) => false,
          );
        } else {
          throw Exception(responseData['message'] ?? 'SSO verification failed');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('SSO Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() => _isLoading = false);
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 20),
                  Text('Authenticating via SSO...'),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('No SSO token found'),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      // Optional: Add manual login fallback here
                    },
                    child: Text('Try Again'),
                  ),
                ],
              ),
      ),
    );
  }
}