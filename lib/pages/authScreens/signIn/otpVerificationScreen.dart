import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/constants/colors.dart';
import 'package:monova_ai_stylist/constants/typography.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'package:monova_ai_stylist/pages/newOnboardingPages/StyleCompanionPage.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/widgets/bottomNavBar/bottomNavBar.dart';

class SignInOtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final String userId;

  const SignInOtpVerificationScreen({
    super.key,
    required this.phoneNumber,
    required this.userId,
  });

  @override
  State<SignInOtpVerificationScreen> createState() =>
      _SignInOtpVerificationScreenState();
}

class _SignInOtpVerificationScreenState
    extends State<SignInOtpVerificationScreen> {
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(
    6,
    (index) => FocusNode(),
  );
  bool _isButtonEnabled = false;
  bool _isLoading = false;
  int? _resendTimer;

  @override
  void initState() {
    super.initState();
    for (var controller in _otpControllers) {
      controller.addListener(_validateOTP);
    }
    _startResendTimer();
  }

  void _startResendTimer() {
    setState(() {
      _resendTimer = 30;
    });
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return false;
      setState(() {
        if (_resendTimer != null && _resendTimer! > 0) {
          _resendTimer = _resendTimer! - 1;
        }
      });
      return _resendTimer != null && _resendTimer! > 0;
    });
  }

  void _validateOTP() {
    String otp = _otpControllers.map((controller) => controller.text).join();
    setState(() {
      _isButtonEnabled = otp.length == 6;
    });
  }

  Future<void> _handleResendOTP() async {
    if (_resendTimer != null && _resendTimer! > 0) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.authApi}auth/otp/login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': widget.userId,
        }),
      );

      if (response.statusCode == 200) {
        _startResendTimer();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('OTP resent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to resend OTP. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleVerifyOTP() async {
  String otp = _otpControllers.map((controller) => controller.text.trim()).join();

  // Skip actual API verification and accept any number with OTP 000000
  if (otp == "000000") {
    // Create mock auth data
    await SharedPrefsService.saveAuthData(
      authToken: "mock-auth-token",
      userId: widget.userId,
      fullName: "Test User",
    );

    if (!mounted) return;
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => BottomNavScreen()),
      (route) => false,
    );
    return;
  }

  setState(() {
    _isLoading = true;
  });

  try {
    final response = await http
        .post(
          Uri.parse('${ApiConstants.authApi}auth/otp/verify'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            "userId": widget.userId,
            "otp": otp,
          }),
        )
        .timeout(const Duration(seconds: 10));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['message'] == 'OTP verified successfully') {
        await SharedPrefsService.saveAuthData(
          authToken: data['data']['authToken'],
          userId: data['data']['userId'],
          fullName: data['data']['userFullName'],
        );

        if (!mounted) return;
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => StyleCompanionPage()),
          (route) => false,
        );
      }
    } else {
      throw Exception('Failed to verify OTP');
    }
  } catch (e) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Network error. Please try again.'),
        backgroundColor: Colors.red,
      ),
    );
  } finally {
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

  Widget _buildOTPTextField(int index) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFEEEEEE)),
      ),
      child: TextField(
        cursorColor: const Color(0xFFFF5722),
        controller: _otpControllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 20,
          fontFamily: "SatoshiM",
          color: Color(0xFF1F1F1F),
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.symmetric(vertical: 12),
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) {
          if (value.isNotEmpty && index < 5) {
            _focusNodes[index + 1].requestFocus();
          } else if (value.isEmpty && index > 0) {
            _focusNodes[index - 1].requestFocus();
          }
        },
      ),
    );
  }

  String _formatPhoneNumber() {
    String phone = widget.phoneNumber;
    if (phone.length > 2) {
      return "********${phone.substring(phone.length - 2)}";
    }
    return phone;
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      isLoading: _isLoading,
      gifAsset: 'assets/loader/loading.gif',
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1F1F1F)),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  Text(
                    'Enter the six code digit code sent to ${_formatPhoneNumber()}.',
                    style: AppTypography.h2.copyWith(color: AppColors.dark),
                  ),
                  const SizedBox(height: 40),
                  Text(
                    'Enter OTP',
                    style: AppTypography.h4.copyWith(color: AppColors.dark),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(
                      6,
                      (index) => _buildOTPTextField(index),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: _resendTimer == 0 ? _handleResendOTP : null,
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey,
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        _resendTimer == 0 ? 'Resend OTP' : 'Resend OTP',
                        style: AppTypography.body1
                            .copyWith(color: AppColors.medium),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isButtonEnabled ? _handleVerifyOTP : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isButtonEnabled
                            ? const Color(0xFFFF5722)
                            : const Color(0xFFE0E0E0),
                        disabledForegroundColor: Colors.grey.shade400,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Log In',
                        style: TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 16,
                          color: _isButtonEnabled
                              ? Colors.white
                              : Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _resendTimer = null;
    super.dispose();
  }
}
