import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/authScreens/signIn/loginScreen.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'otpVerificationScreen.dart';

class SignUpScreen extends StatefulWidget {
  final String initialName;
  final Map<String, dynamic> styleGuideData;

  const SignUpScreen({
    super.key,
    this.initialName = '',
    required this.styleGuideData,
  });

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  late TextEditingController _fullNameController;
  final TextEditingController _phoneController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isButtonEnabled = false;
  bool _isLoading = false;
  bool _autoValidate = false;

  @override
  void initState() {
    super.initState();
    _fullNameController = TextEditingController(text: widget.initialName);
    _phoneController.addListener(_validateFields);
    _fullNameController.addListener(_validateFields);

    // If initialName is provided, check button status immediately
    if (widget.initialName.isNotEmpty) {
      _validateFields();
    }
  }

  void _validateFields() {
    setState(() {
      _isButtonEnabled = _phoneController.text.length == 10 &&
          _fullNameController.text.trim().isNotEmpty;
    });
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    if (!RegExp(r'^[a-zA-Z ]+$').hasMatch(value)) {
      return 'Only letters and spaces are allowed';
    }
    return null;
  }

  Future<void> _handleGetOTP() async {
    // Close the keyboard
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) {
      setState(() {
        _autoValidate = true;
      });
      return;
    }

    final phoneNumber = _phoneController.text.trim();
    final fullName = _fullNameController.text.trim();

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.authApi}auth/otp/signup'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': '91$phoneNumber',
          'userFullName': fullName,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['message'] == 'OTP sent successfully') {
          final userId = data['data']['userId'];

          if (!mounted) return;

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OTPVerificationScreen(
                phoneNumber: phoneNumber,
                userId: userId,
                fullName: fullName,
                styleGuideData: widget.styleGuideData,
              ),
            ),
          );
        } else {
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send OTP. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Error: ${jsonDecode(response.body)['message'] ?? 'Failed to send OTP'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Network error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      isLoading: _isLoading,
      gifAsset: 'assets/loader/loading.gif',
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1F1F1F)),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: _formKey,
                  autovalidateMode: _autoValidate
                      ? AutovalidateMode.onUserInteraction
                      : AutovalidateMode.disabled,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      const Text(
                        'Sign up to save your guide.',
                        style: TextStyle(
                          fontFamily: "SatoshiB",
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F1F1F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Let\'s make you an account.',
                        style: TextStyle(
                          fontFamily: "SatoshiR",
                          fontSize: 18,
                          color: Color(0xFF4A4A4A),
                        ),
                      ),
                      const SizedBox(height: 40),
                      const Text(
                        'Name',
                        style: TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F1F1F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        cursorColor: const Color(0xFFFF5722),
                        controller: _fullNameController,
                        validator: _validateName,
                        textCapitalization: TextCapitalization.words,
                        style: const TextStyle(
                          fontSize: 16,
                          fontFamily: "SatoshiR",
                          color: Color(0xFF1F1F1F),
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter your full name',
                          hintStyle: const TextStyle(
                            fontFamily: "SatoshiR",
                            color: Color(0xFFAAAAAA),
                            fontSize: 16,
                          ),
                          filled: true,
                          fillColor: const Color(0xFFF5F5F5),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'[a-zA-Z ]')),
                        ],
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'Mobile number',
                        style: TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F1F1F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(left: 16),
                              child: Text(
                                '+91',
                                style: TextStyle(
                                  fontFamily: "SatoshiM",
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F1F1F),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextField(
                                cursorColor: const Color(0xFFFF5722),
                                controller: _phoneController,
                                keyboardType: TextInputType.number,
                                maxLength: 10,
                                style: const TextStyle(
                                  fontFamily: "SatoshiR",
                                  fontSize: 16,
                                  color: Color(0xFF1F1F1F),
                                ),
                                decoration: const InputDecoration(
                                  hintText: '0000000000',
                                  hintStyle: TextStyle(
                                    fontFamily: "SatoshiR",
                                    color: Color(0xFFAAAAAA),
                                    fontSize: 16,
                                  ),
                                  border: InputBorder.none,
                                  counterText: '',
                                  contentPadding:
                                      EdgeInsets.symmetric(vertical: 16),
                                ),
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'We will send an OTP to verify your phone number. Standard message and data rates may apply.',
                        style: TextStyle(
                          fontFamily: "SatoshiR",
                          fontSize: 12,
                          color: Color(0xFF8E8E8E),
                        ),
                      ),
                      const SizedBox(height: 40),
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: _isButtonEnabled ? _handleGetOTP : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _isButtonEnabled
                                ? const Color(0xFFFF5722)
                                : const Color(0xFFE0E0E0),
                            disabledForegroundColor: Colors.grey.shade400,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Get OTP',
                            style: TextStyle(
                              fontFamily: "SatoshiM",
                              fontSize: 16,
                              color: _isButtonEnabled
                                  ? Colors.white
                                  : Colors.grey.shade700,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),
                      Center(
                        child: TextButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SignInLoginScreen(),
                              ),
                            );
                          },
                          child: const Text(
                            "Already have an account? Sign In",
                            style: TextStyle(
                              fontFamily: "SatoshiR",
                              color: Color(0xFFFF5722),
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }
}
