import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:monova_ai_stylist/constants/apiConstants.dart';
import 'package:monova_ai_stylist/pages/loaderPage/loaderPage.dart';
import 'package:monova_ai_stylist/services/sharedprefsservice.dart';
import 'package:monova_ai_stylist/widgets/bottomNavBar/bottomNavBar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final String userId;
  final String fullName;
  final Map<String, dynamic>? styleGuideData;

  const OTPVerificationScreen({
    super.key,
    required this.phoneNumber,
    required this.userId,
    required this.fullName,
    this.styleGuideData,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(
    6,
    (index) => FocusNode(),
  );
  bool _isButtonEnabled = false;
  bool _isLoading = false;
  int _remainingSeconds = 30;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    for (var controller in _otpControllers) {
      controller.addListener(_validateOTP);
    }
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  void _validateOTP() {
    String otp = _otpControllers.map((controller) => controller.text).join();
    setState(() {
      _isButtonEnabled = otp.length == 6;
    });
  }

  Future<void> _handleResendOTP() async {
    setState(() {
      _isLoading = true;
      _remainingSeconds = 30;
    });

    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.authApi}auth/otp/signup'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': widget.userId,
          'userFullName': widget.fullName,
        }),
      );

      if (response.statusCode == 200) {
        _startTimer();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('OTP resent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to resend OTP. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveStyleProfile(String authToken) async {
    if (widget.styleGuideData == null) return;

    try {
      // Get user data from SharedPreferences to merge with style guide data
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('userData');
      Map<String, dynamic> userDataMap = {};

      if (userData != null) {
        userDataMap = jsonDecode(userData);
      }

      // Ensure we're using the cloud URL from Azure, not the local path
      String profileImageUrl = userDataMap["userProfileImageUrl"] ?? "";

      // Validate the URL is from Azure blob storage
      if (profileImageUrl.isEmpty ||
          !profileImageUrl
              .startsWith('https://monovaoutfits.blob.core.windows.net')) {
        print(
            'WARNING: Invalid profile image URL in _saveStyleProfile: $profileImageUrl');
        // If the URL is invalid, don't send it to the API
        profileImageUrl = "";
      } else {
        print('Using valid Azure URL in style profile: $profileImageUrl');
      }

      // Create request body by merging user data and style guide data
      final requestBody = {
        "userId": widget.userId,
        "userAge": userDataMap["userAge"],
        "userGender": userDataMap["userGender"],
        "userUndertone": userDataMap["userUndertone"],
        "userSkinTone": userDataMap["userSkinTone"],
        "userBodyType": userDataMap["userBodyType"],
        "userHeight": userDataMap["userHeight"],
        "userHeightMetric": userDataMap["userHeightMetric"],
        "userEyeColor": userDataMap["userEyeColor"],
        "userHairColor": userDataMap["userHairColor"],
        "userContrast": userDataMap["userContrast"],
        "userSeason": userDataMap["userSeason"],
        "userProfileImageUrl": profileImageUrl,
        "styleGuide": widget.styleGuideData!['data'],
      };

      // Make API call to save style profile
      final response = await http.post(
        Uri.parse('${ApiConstants.styleProfileApi}style-profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 201) {
        print('Style profile saved successfully: ${response.body}');
      } else {
        print('Failed to save style profile: ${response.body}');
      }
    } catch (e) {
      print('Error saving style profile: $e');
    }
  }

  Future<void> _handleVerifyOTP() async {
    String otp =
        _otpControllers.map((controller) => controller.text.trim()).join();

    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.authApi}auth/otp/verify'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': widget.userId,
          'otp': otp,
          'userFullName': widget.fullName,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 &&
          data['message'] == 'OTP verified successfully') {
        final authToken = data['data']['authToken'];
        final userId = data['data']['userId'];
        final fullName = data['data']['userFullName'];

        // Save auth data in shared preferences
        await SharedPrefsService.saveAuthData(
          authToken: authToken,
          userId: userId,
          fullName: fullName,
        );

        // Save style profile if style guide data is available
        if (widget.styleGuideData != null) {
          await _saveStyleProfile(authToken);
        }

        if (!mounted) return;

        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const BottomNavScreen()),
          (route) => false,
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(data['message'] ?? 'Failed to verify OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Network error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatPhoneNumber() {
    String phone = widget.phoneNumber;
    if (phone.length > 2) {
      return "********${phone.substring(phone.length - 2)}";
    }
    return phone;
  }

  Widget _buildOTPTextField(int index) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFEEEEEE)),
      ),
      child: TextField(
        cursorColor: const Color(0xFFFF5722),
        controller: _otpControllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 20,
          fontFamily: "SatoshiM",
          color: Color(0xFF1F1F1F),
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.symmetric(vertical: 12),
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) {
          if (value.isNotEmpty && index < 5) {
            _focusNodes[index + 1].requestFocus();
          } else if (value.isEmpty && index > 0) {
            _focusNodes[index - 1].requestFocus();
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      isLoading: _isLoading,
      gifAsset: 'assets/loader/loading.gif',
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1F1F1F)),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  Text(
                    'Enter the six code digit code sent to ${_formatPhoneNumber()}.',
                    style: const TextStyle(
                      fontFamily: "SatoshiB",
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1F1F1F),
                    ),
                  ),
                  const SizedBox(height: 40),
                  const Text(
                    'Enter OTP',
                    style: TextStyle(
                      fontFamily: "SatoshiM",
                      fontSize: 16,
                      color: Color(0xFF1F1F1F),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(
                      6,
                      (index) => _buildOTPTextField(index),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed:
                          _remainingSeconds == 0 ? _handleResendOTP : null,
                      style: TextButton.styleFrom(
                        foregroundColor: _remainingSeconds == 0
                            ? const Color(0xFFFF5722)
                            : Colors.grey,
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        _remainingSeconds == 0
                            ? 'Resend OTP'
                            : 'Resend OTP in $_remainingSeconds seconds',
                        style: TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 14,
                          color: _remainingSeconds == 0
                              ? const Color(0xFFFF5722)
                              : Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isButtonEnabled ? _handleVerifyOTP : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isButtonEnabled
                            ? const Color(0xFFFF5722)
                            : const Color(0xFFE0E0E0),
                        disabledForegroundColor: Colors.grey.shade400,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Sign Up',
                        style: TextStyle(
                          fontFamily: "SatoshiM",
                          fontSize: 16,
                          color: _isButtonEnabled
                              ? Colors.white
                              : Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }
}
