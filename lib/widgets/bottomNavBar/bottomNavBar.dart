import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:monova_ai_stylist/models/chatModels/message.dart';
import 'package:monova_ai_stylist/pages/chatScreen/chatScreen.dart';
import 'package:monova_ai_stylist/pages/features/wadrobe/search/viadesc.dart';
import 'package:monova_ai_stylist/pages/homeScreen/HomeScreen.dart';
import 'package:monova_ai_stylist/pages/profilePage/profileScreen.dart';
import 'package:monova_ai_stylist/services/websocketService.dart';
import 'package:url_launcher/url_launcher.dart';

class BottomNavScreen extends StatefulWidget {
  final int initialIndex;
  final bool openChatScreen;
  final String? initialChatMessage; // Add parameter for initial message

  const BottomNavScreen({
    Key? key,
    this.initialIndex = 0,
    this.openChatScreen = false,
    this.initialChatMessage, // New parameter
  }) : super(key: key);

  @override
  _BottomNavScreenState createState() => _BottomNavScreenState();
}

class _BottomNavScreenState extends State<BottomNavScreen>
    with TickerProviderStateMixin {
  late int _selectedIndex;
  late AnimationController _animationController;

  final List<String> _wearSuggestions = [
    "for party outfits",
    "for date fit check",
    "for coffee outfit ideas",
    "for cute summer outfits",
    "for workwear suggestions",
  ];

  int _currentSuggestionIndex = 0;
  int _nextSuggestionIndex = 1;
  late Timer _suggestionTimer;

  late AnimationController _textAnimController;
  late Animation<Offset> _currentTextOffsetAnimation;
  late Animation<Offset> _nextTextOffsetAnimation;
  late Animation<double> _currentTextOpacityAnimation;
  late Animation<double> _nextTextOpacityAnimation;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: 3),
    )..repeat();

    _textAnimController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 700),
    );

    _currentTextOffsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(0, -1.5),
    ).animate(CurvedAnimation(
      parent: _textAnimController,
      curve: Curves.easeOutCubic,
    ));

    _currentTextOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0,
    ).animate(_textAnimController);

    _nextTextOffsetAnimation = Tween<Offset>(
      begin: Offset(0, 1.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textAnimController,
      curve: Curves.easeOutCubic,
    ));

    _nextTextOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0,
    ).animate(_textAnimController);

    _suggestionTimer = Timer.periodic(Duration(seconds: 2), (timer) {
      setState(() {
        _nextSuggestionIndex =
            (_currentSuggestionIndex + 1) % _wearSuggestions.length;
      });

      _textAnimController.reset();
      _textAnimController.forward();
    });

    _textAnimController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentSuggestionIndex = _nextSuggestionIndex;
        });
      }
    });

    // Handle chat opening with initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.openChatScreen) {
        // If there's an initial message, open chat with that message
        if (widget.initialChatMessage != null &&
            widget.initialChatMessage!.isNotEmpty) {
          _openChatWithInitialMessage(widget.initialChatMessage!);
        } else {
          // Just open chat without message
          Future.delayed(Duration(milliseconds: 300), () {
            if (mounted) {
              _launchChatUrl();
            }
          });
        }
      }
    });
  }

  Future<void> _launchChatUrl() async {
    final url = Uri.parse('https://link.monova.in/hi-nova');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  // New method to open chat with initial message
  void _openChatWithInitialMessage(String message) {
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) {
        _launchChatUrl();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _textAnimController.dispose();
    _suggestionTimer.cancel();
    super.dispose();
  }

  static final List<Widget> _widgetOptions = <Widget>[
    // HomeScreen(),
    WadrobeHomeScreen(),
    ProfileScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _widgetOptions.elementAt(_selectedIndex),
      bottomNavigationBar: Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Search Bar with animated gradient border
            GestureDetector(
              onTap: _launchChatUrl,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 15, 24, 8),
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFF76037), // Orange
                            Color(0xFFFFD700), // Yellow
                          ],
                          stops: [0, 1.0],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          transform: GradientRotation(
                              _animationController.value * 6.28),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(1.5),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 16.0, right: 8.0),
                                child: Image.asset("assets/icons/star.png",
                                    height: 20, width: 20),
                              ),
                              const Text(
                                "Ask Nova",
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                  fontFamily: "SatoshiR",
                                ),
                              ),
                              AnimatedBuilder(
                                animation: _textAnimController,
                                builder: (context, child) {
                                  return SizedBox(
                                    width: 180,
                                    height: 30,
                                    child: ClipRect(
                                      child: Stack(
                                        children: [
                                          SlideTransition(
                                            position:
                                                _currentTextOffsetAnimation,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Text(
                                                " ${_wearSuggestions[_currentSuggestionIndex]}",
                                                style: const TextStyle(
                                                  color: Colors.black54,
                                                  fontSize: 16,
                                                  fontFamily: "SatoshiR",
                                                  fontStyle: FontStyle.italic,
                                                ),
                                              ),
                                            ),
                                          ),
                                          SlideTransition(
                                            position: _nextTextOffsetAnimation,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Text(
                                                " ${_wearSuggestions[_nextSuggestionIndex]}",
                                                style: const TextStyle(
                                                  color: Colors.black54,
                                                  fontSize: 16,
                                                  fontFamily: "SatoshiR",
                                                  fontStyle: FontStyle.italic,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                              const Spacer(),
                              const Padding(
                                padding: EdgeInsets.only(right: 16.0),
                                child: Icon(
                                  FeatherIcons.search,
                                  color: Colors.black,
                                  size: 24,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Bottom Navigation Bar
            SizedBox(
              height: kIsWeb
                  ? 70
                  : Platform.isIOS
                      ? 95
                      : 70,
              child: BottomNavigationBar(
                elevation: 0,
                backgroundColor: Colors.white,
                items: const <BottomNavigationBarItem>[
                  // BottomNavigationBarItem(
                  //   icon: Icon(FeatherIcons.home),
                  //   label: 'Home',
                  // ),
                  BottomNavigationBarItem(
                    icon: Icon(FeatherIcons.columns),
                    label: 'Wardrobe',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.account_circle_outlined),
                    label: 'Profile',
                  ),
                ],
                currentIndex: _selectedIndex,
                selectedItemColor: Color(0xffF76037),
                onTap: _onItemTapped,
              ),
            ),
          ],
        ),
      ),
    );
  }
}