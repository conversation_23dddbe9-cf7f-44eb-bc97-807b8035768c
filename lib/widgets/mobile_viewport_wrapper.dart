import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:monova_ai_stylist/config/mobile_viewport_config.dart';

class MobileViewportWrapper extends StatelessWidget {
  final Widget child;
  final double mobileWidth;
  final bool showPhoneFrame;
  final Color backgroundColor;

  const MobileViewportWrapper({
    super.key,
    required this.child,
    this.mobileWidth = MobileViewportConfig.defaultMobileWidth,
    this.showPhoneFrame = true,
    this.backgroundColor = MobileViewportConfig.frameBackgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    // Only apply mobile constraints on web platform
    if (!kIsWeb) {
      return child;
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // If screen is already mobile-sized, don't apply constraints
    if (screenWidth <= mobileWidth + 100) {
      return child;
    }

    // Calculate mobile viewport height (maintain aspect ratio)
    final mobileHeight = screenHeight;

    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: Container(
          width: mobileWidth,
          height: mobileHeight,
          decoration: showPhoneFrame
              ? BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(MobileViewportConfig.frameRadius),
                  boxShadow: MobileViewportConfig.phoneShadow,
                  border: Border.all(
                    color: MobileViewportConfig.frameBorderColor,
                    width: 1,
                  ),
                )
              : BoxDecoration(
                  color: Colors.white,
                  boxShadow: MobileViewportConfig.subtleShadow,
                ),
          child: ClipRRect(
            borderRadius: showPhoneFrame
                ? BorderRadius.circular(MobileViewportConfig.frameRadius)
                : BorderRadius.zero,
            child: child,
          ),
        ),
      ),
    );
  }
}

class ResponsiveMobileWrapper extends StatelessWidget {
  final Widget child;
  final double maxMobileWidth;
  final bool enablePhoneFrame;

  const ResponsiveMobileWrapper({
    super.key,
    required this.child,
    this.maxMobileWidth = MobileViewportConfig.iPhoneProMaxWidth,
    this.enablePhoneFrame = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        
        // On web and desktop, constrain to mobile width
        if (kIsWeb && MobileViewportConfig.shouldShowMobileFrame(screenWidth)) {
          return MobileViewportWrapper(
            mobileWidth: maxMobileWidth,
            showPhoneFrame: enablePhoneFrame,
            child: child,
          );
        }
        
        // On mobile or small screens, use full width
        return child;
      },
    );
  }
}
